# Test Reports และ Test Summaries

โฟลเดอร์นี้เก็บข้อมูลสรุปการทดสอบ รายงานผล และ test coverage สำหรับโปรเจค AAMG

## โครงสร้างโฟลเดอร์

```
test_reports/
├── README.md                    # ไฟล์นี้
├── coverage/                    # รายงาน test coverage
│   ├── html/                   # HTML coverage reports
│   ├── lcov.info              # LCOV coverage data
│   └── coverage_summary.json  # สรุป coverage ในรูปแบบ JSON
├── unit_tests/                 # รายงานการทดสอบ unit tests
│   ├── branch_screen_tests.md  # สรุปการทดสอบ BranchScreen
│   ├── branch_controller_tests.md # สรุปการทดสอบ BranchController
│   └── test_results.json      # ผลการทดสอบในรูปแบบ JSON
├── widget_tests/               # รายงานการทดสอบ widget tests
│   ├── ui_component_tests.md   # สรุปการทดสอบ UI components
│   └── interaction_tests.md    # สรุปการทดสอบ user interactions
├── integration_tests/          # รายงานการทดสอบ integration tests
│   ├── api_integration_tests.md # สรุปการทดสอบ API integration
│   └── end_to_end_tests.md     # สรุปการทดสอบ end-to-end
└── performance_tests/          # รายงานการทดสอบ performance
    ├── load_time_tests.md      # สรุปการทดสอบ load time
    └── memory_usage_tests.md   # สรุปการทดสอบ memory usage
```

## วิธีการใช้งาน

### 1. การรันการทดสอบ
```bash
# รัน unit tests
flutter test

# รัน tests พร้อม coverage
flutter test --coverage

# รัน widget tests
flutter test test/widget_test/

# รัน integration tests
flutter test test/integration_test/
```

### 2. การสร้าง coverage report
```bash
# สร้าง HTML coverage report
genhtml coverage/lcov.info -o test_reports/coverage/html/

# ดู coverage report
open test_reports/coverage/html/index.html
```

### 3. การอัพเดทรายงาน
- รายงานจะถูกอัพเดทอัตโนมัติหลังจากการรันการทดสอบ
- สามารถดูประวัติการทดสอบได้จากไฟล์ในแต่ละโฟลเดอร์

## เป้าหมาย Test Coverage
- Unit Tests: >= 80%
- Widget Tests: >= 70%
- Integration Tests: >= 60%
- Overall Coverage: >= 75%

## การติดตาม Test Quality
- ตรวจสอบ test coverage อย่างสม่ำเสมอ
- อัพเดท test cases เมื่อมีการเปลี่ยนแปลง code
- รักษาคุณภาพของ test cases ให้อยู่ในระดับสูง
