import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:AAMG/view/screen/branch/branch_screen.dart';
import 'package:AAMG/controller/branch/branch.controller.dart';
import 'package:AAMG/models/branch/branch_model.dart';
import '../mocks/mock_branch_controller.dart';
import '../helpers/test_helpers.dart';

void main() {
  group('BranchScreen Unit Tests', () {
    late MockBranchController mockController;

    setUp(() {
      TestHelpers.setupGetXForTesting();
      mockController = MockBranchController();
      Get.put<BranchController>(mockController);
    });

    tearDown(() {
      TestHelpers.cleanupGetX();
    });

    group('Widget Creation Tests', () {
      testWidgets('should create BranchScreen widget successfully', (WidgetTester tester) async {
        // Arrange
        await mockController.getBranchData();

        // Act
        await tester.pumpWidget(
          TestHelpers.createTestableWidget(BranchScreen()),
        );
        await TestHelpers.waitForAnimations(tester);

        // Assert
        expect(find.byType(BranchScreen), findsOneWidget);
        expect(find.byType(Scaffold), findsOneWidget);
      });

      testWidgets('should display header with correct title', (WidgetTester tester) async {
        // Arrange
        await mockController.getBranchData();

        // Act
        await tester.pumpWidget(
          TestHelpers.createTestableWidget(BranchScreen()),
        );
        await TestHelpers.waitForAnimations(tester);

        // Assert
        expect(find.byType(AppBar), findsOneWidget);
        // Note: ต้องเพิ่มการตรวจสอบ title ตาม translation key
      });

      testWidgets('should display search field', (WidgetTester tester) async {
        // Arrange
        await mockController.getBranchData();

        // Act
        await tester.pumpWidget(
          TestHelpers.createTestableWidget(BranchScreen()),
        );
        await TestHelpers.waitForAnimations(tester);

        // Assert
        expect(find.byType(TextFormField), findsOneWidget);
      });
    });

    group('Search Functionality Tests', () {
      testWidgets('should activate search zone when tapping search field', (WidgetTester tester) async {
        // Arrange
        await mockController.getBranchData();
        await tester.pumpWidget(
          TestHelpers.createTestableWidget(BranchScreen()),
        );
        await TestHelpers.waitForAnimations(tester);

        // Act
        await TestHelpers.tapAndWait(tester, find.byType(TextFormField));

        // Assert
        expect(mockController.isActiveSearchZone.value, isTrue);
      });

      testWidgets('should search when entering 3 or more characters', (WidgetTester tester) async {
        // Arrange
        await mockController.getBranchData();
        await tester.pumpWidget(
          TestHelpers.createTestableWidget(BranchScreen()),
        );
        await TestHelpers.waitForAnimations(tester);

        // Act
        await TestHelpers.enterTextAndWait(tester, find.byType(TextFormField), 'ทดสอบ');

        // Assert
        expect(mockController.getLastSearchKeyword, equals('ทดสอบ'));
      });

      testWidgets('should not search when entering less than 3 characters', (WidgetTester tester) async {
        // Arrange
        await mockController.getBranchData();
        await tester.pumpWidget(
          TestHelpers.createTestableWidget(BranchScreen()),
        );
        await TestHelpers.waitForAnimations(tester);

        // Act
        await TestHelpers.enterTextAndWait(tester, find.byType(TextFormField), 'ab');

        // Assert
        expect(mockController.getLastSearchKeyword, isNull);
      });

      testWidgets('should reset to initial data when clearing search', (WidgetTester tester) async {
        // Arrange
        await mockController.getBranchData();
        await tester.pumpWidget(
          TestHelpers.createTestableWidget(BranchScreen()),
        );
        await TestHelpers.waitForAnimations(tester);

        // Act - First search
        await TestHelpers.enterTextAndWait(tester, find.byType(TextFormField), 'ทดสอบ');
        // Then clear
        await TestHelpers.enterTextAndWait(tester, find.byType(TextFormField), '');

        // Assert
        expect(mockController.branchDataList!.length, equals(mockController.branchInitList!.length));
      });
    });

    group('Filter Functionality Tests', () {
      testWidgets('should show filter button for AAM configuration', (WidgetTester tester) async {
        // Arrange
        await mockController.getBranchData();

        // Act
        await tester.pumpWidget(
          TestHelpers.createTestableWidget(BranchScreen()),
        );
        await TestHelpers.waitForAnimations(tester);

        // Assert
        // Note: ต้องเพิ่มการ mock appConfigService.countryConfigCollection
        // expect(find.byType(InkWell), findsWidgets);
      });

      testWidgets('should activate select zone when tapping filter button', (WidgetTester tester) async {
        // Arrange
        await mockController.getBranchData();
        await tester.pumpWidget(
          TestHelpers.createTestableWidget(BranchScreen()),
        );
        await TestHelpers.waitForAnimations(tester);

        // Act
        // Note: ต้องหา filter button และ tap
        // await TestHelpers.tapAndWait(tester, find.byIcon(Icons.filter));

        // Assert
        // expect(mockController.isActiveSelectZone.value, isTrue);
      });
    });

    group('Data Display Tests', () {
      testWidgets('should display branch list when data is loaded', (WidgetTester tester) async {
        // Arrange
        await mockController.getBranchData();

        // Act
        await tester.pumpWidget(
          TestHelpers.createTestableWidget(BranchScreen()),
        );
        await TestHelpers.waitForAnimations(tester);

        // Assert
        expect(find.byType(ListView), findsOneWidget);
        expect(mockController.hasData, isTrue);
        expect(mockController.dataCount, greaterThan(0));
      });

      testWidgets('should display correct number of branch items', (WidgetTester tester) async {
        // Arrange
        final mockData = TestHelpers.createMockBranchData();
        mockController.setMockData(mockData);

        // Act
        await tester.pumpWidget(
          TestHelpers.createTestableWidget(BranchScreen()),
        );
        await TestHelpers.waitForAnimations(tester);

        // Assert
        expect(mockController.dataCount, equals(mockData.length));
      });
    });

    group('Refresh Functionality Tests', () {
      testWidgets('should refresh data when pull to refresh', (WidgetTester tester) async {
        // Arrange
        await mockController.getBranchData();
        await tester.pumpWidget(
          TestHelpers.createTestableWidget(BranchScreen()),
        );
        await TestHelpers.waitForAnimations(tester);

        // Act
        await tester.fling(find.byType(RefreshIndicator), const Offset(0, 300), 1000);
        await TestHelpers.waitForAnimations(tester);

        // Assert
        expect(mockController.selectedZone.value, equals(''));
        expect(mockController.isActiveSelectZone.value, isFalse);
        expect(mockController.isActiveSearchZone.value, isFalse);
      });
    });

    group('Navigation Tests', () {
      testWidgets('should navigate back when back button is pressed', (WidgetTester tester) async {
        // Arrange
        await mockController.getBranchData();
        await tester.pumpWidget(
          TestHelpers.createTestableWidget(BranchScreen()),
        );
        await TestHelpers.waitForAnimations(tester);

        // Act
        // Note: ต้องหา back button และ tap
        // await TestHelpers.tapAndWait(tester, find.byIcon(Icons.arrow_back));

        // Assert
        // ตรวจสอบการนำทาง (ต้องใช้ NavigatorObserver)
      });
    });

    group('Error Handling Tests', () {
      testWidgets('should handle API error gracefully', (WidgetTester tester) async {
        // Arrange
        mockController.simulateApiError();

        // Act & Assert
        expect(() async {
          await mockController.getBranchData();
        }, throwsException);
      });

      testWidgets('should handle search API error gracefully', (WidgetTester tester) async {
        // Arrange
        mockController.simulateApiError();
        await tester.pumpWidget(
          TestHelpers.createTestableWidget(BranchScreen()),
        );
        await TestHelpers.waitForAnimations(tester);

        // Act & Assert
        expect(() async {
          await mockController.searchBranchData('test');
        }, throwsException);
      });
    });
  });
}
