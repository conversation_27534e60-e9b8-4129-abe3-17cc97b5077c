import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:AAMG/view/screen/loan_screen/loan_main_screen.dart';
import 'package:AAMG/controller/contract/contractlist.controller.dart';
import 'package:AAMG/controller/home/<USER>';
import 'package:AAMG/controller/profile/profile.controller.dart';
import 'package:AAMG/controller/request_loan/loan.controller.dart';
import '../mocks/mock_contract_list_controller.dart';
import '../mocks/mock_home_controller.dart';
import '../mocks/mock_profile_controller.dart';
import '../mocks/mock_loan_controller.dart';
import '../helpers/test_helpers.dart';

void main() {
  group('LoanMainScreen Unit Tests', () {
    late MockContractListController mockContractListController;
    late MockHomeController mockHomeController;
    late MockProfileController mockProfileController;
    late MockLoanController mockLoanController;

    setUp(() {
      TestHelpers.setupGetXForTesting();
      
      mockContractListController = MockContractListController();
      mockHomeController = MockHomeController();
      mockProfileController = MockProfileController();
      mockLoanController = MockLoanController();
      
      Get.put<ContractListController>(mockContractListController);
      Get.put<HomeController>(mockHomeController);
      Get.put<ProfileController>(mockProfileController);
      Get.put<LoanController>(mockLoanController);
    });

    tearDown(() {
      TestHelpers.cleanupGetX();
    });

    group('Widget Creation Tests', () {
      testWidgets('should create LoanMainScreen widget successfully', (WidgetTester tester) async {
        // Arrange
        mockContractListController.setMockContractData([]);
        mockContractListController.setMockContractStatusData([]);

        // Act
        await tester.pumpWidget(
          TestHelpers.createTestableWidget(const LoanMainScreen()),
        );
        await TestHelpers.waitForAnimations(tester);

        // Assert
        expect(find.byType(LoanMainScreen), findsOneWidget);
        expect(find.byType(Scaffold), findsOneWidget);
      });

      testWidgets('should display header with correct title', (WidgetTester tester) async {
        // Arrange
        mockContractListController.setMockContractData([]);
        mockContractListController.setMockContractStatusData([]);

        // Act
        await tester.pumpWidget(
          TestHelpers.createTestableWidget(const LoanMainScreen()),
        );
        await TestHelpers.waitForAnimations(tester);

        // Assert
        expect(find.byType(Text), findsWidgets);
        // Note: ต้องเพิ่มการตรวจสอบ title ตาม translation key
      });

      testWidgets('should display primary button', (WidgetTester tester) async {
        // Arrange
        mockContractListController.setMockContractData([]);
        mockContractListController.setMockContractStatusData([]);

        // Act
        await tester.pumpWidget(
          TestHelpers.createTestableWidget(const LoanMainScreen()),
        );
        await TestHelpers.waitForAnimations(tester);

        // Assert
        expect(find.byType(ElevatedButton), findsWidgets);
      });
    });

    group('Loan Default State Tests', () {
      testWidgets('should display loan default view when no contracts', (WidgetTester tester) async {
        // Arrange
        mockContractListController.setMockContractData([]);
        mockContractListController.setMockContractStatusData([]);

        // Act
        await tester.pumpWidget(
          TestHelpers.createTestableWidget(const LoanMainScreen()),
        );
        await TestHelpers.waitForAnimations(tester);

        // Assert
        expect(find.byType(Image), findsWidgets); // Loan default image
        expect(find.text('ยังไม่มีรายการขอกู้เงิน'), findsOneWidget);
      });

      testWidgets('should display correct image based on app configuration', (WidgetTester tester) async {
        // Arrange
        mockContractListController.setMockContractData([]);
        mockContractListController.setMockContractStatusData([]);

        // Act
        await tester.pumpWidget(
          TestHelpers.createTestableWidget(const LoanMainScreen()),
        );
        await TestHelpers.waitForAnimations(tester);

        // Assert
        expect(find.byType(Image), findsWidgets);
        // Note: ต้องเพิ่มการตรวจสอบ image path ตาม appConfigService
      });
    });

    group('Loan Request State Tests', () {
      testWidgets('should display loan request view when has contracts', (WidgetTester tester) async {
        // Arrange
        final mockContracts = TestHelpers.createMockContractData();
        mockContractListController.setMockContractData(mockContracts.cast());

        // Act
        await tester.pumpWidget(
          TestHelpers.createTestableWidget(const LoanMainScreen()),
        );
        await TestHelpers.waitForAnimations(tester);

        // Assert
        expect(find.byType(ListView), findsOneWidget);
        expect(mockContractListController.hasContracts, isTrue);
      });

      testWidgets('should display contract cards when has contract data', (WidgetTester tester) async {
        // Arrange
        final mockContracts = TestHelpers.createMockContractData();
        mockContractListController.setMockContractData(mockContracts.cast());

        // Act
        await tester.pumpWidget(
          TestHelpers.createTestableWidget(const LoanMainScreen()),
        );
        await TestHelpers.waitForAnimations(tester);

        // Assert
        expect(find.byType(Container), findsWidgets);
        expect(mockContractListController.contractCount, equals(mockContracts.length));
      });

      testWidgets('should display pending loan cards when has contract status', (WidgetTester tester) async {
        // Arrange
        final mockStatuses = TestHelpers.createMockContractStatusData();
        mockContractListController.setMockContractStatusData(mockStatuses.cast());

        // Act
        await tester.pumpWidget(
          TestHelpers.createTestableWidget(const LoanMainScreen()),
        );
        await TestHelpers.waitForAnimations(tester);

        // Assert
        expect(mockContractListController.hasContractStatus, isTrue);
        expect(mockContractListController.contractStatusCount, equals(mockStatuses.length));
      });
    });

    group('Primary Button Tests', () {
      testWidgets('should show register dialog when user is guest', (WidgetTester tester) async {
        // Arrange
        mockHomeController.setMockGuestStatus(true);
        mockContractListController.setMockContractData([]);

        // Act
        await tester.pumpWidget(
          TestHelpers.createTestableWidget(const LoanMainScreen()),
        );
        await TestHelpers.waitForAnimations(tester);

        // Find and tap primary button
        final primaryButton = find.byType(ElevatedButton).first;
        await TestHelpers.tapAndWait(tester, primaryButton);

        // Assert
        expect(mockHomeController.isGuest!.value, isTrue);
        // Note: ต้องเพิ่มการตรวจสอบ dialog แสดงขึ้น
      });

      testWidgets('should show address update alert when profile incomplete', (WidgetTester tester) async {
        // Arrange
        mockHomeController.setMockGuestStatus(false);
        mockProfileController.setMockCompleteAddress(false);
        mockContractListController.setMockContractData([]);

        // Act
        await tester.pumpWidget(
          TestHelpers.createTestableWidget(const LoanMainScreen()),
        );
        await TestHelpers.waitForAnimations(tester);

        // Find and tap primary button
        final primaryButton = find.byType(ElevatedButton).first;
        await TestHelpers.tapAndWait(tester, primaryButton);

        // Assert
        expect(mockProfileController.hasCompleteAddress, isFalse);
        // Note: ต้องเพิ่มการตรวจสอบ AlertPopup.AlertUpdateAddress
      });

      testWidgets('should proceed to loan agreement when profile complete', (WidgetTester tester) async {
        // Arrange
        mockHomeController.setMockGuestStatus(false);
        mockProfileController.setMockCompleteAddress(true);
        mockContractListController.setMockContractData([]);

        // Act
        await tester.pumpWidget(
          TestHelpers.createTestableWidget(const LoanMainScreen()),
        );
        await TestHelpers.waitForAnimations(tester);

        // Find and tap primary button
        final primaryButton = find.byType(ElevatedButton).first;
        await TestHelpers.tapAndWait(tester, primaryButton);

        // Assert
        expect(mockProfileController.hasCompleteAddress, isTrue);
        // Note: ต้องเพิ่มการตรวจสอบ LoanAgrementWidget.acceptAAMPAYPolicy
      });
    });

    group('Navigation Tests', () {
      testWidgets('should navigate back and reset loan controller when back button pressed', (WidgetTester tester) async {
        // Arrange
        mockContractListController.setMockContractData([]);

        // Act
        await tester.pumpWidget(
          TestHelpers.createTestableWidget(const LoanMainScreen()),
        );
        await TestHelpers.waitForAnimations(tester);

        // Find and tap back button
        final backButton = find.byType(GestureDetector).first;
        await TestHelpers.tapAndWait(tester, backButton);

        // Assert
        expect(mockLoanController.wasResetCalled, isTrue);
        // Note: ต้องเพิ่มการตรวจสอบ navigation ไป HomeNavigator
      });
    });

    group('State Management Tests', () {
      testWidgets('should update UI when contract list changes', (WidgetTester tester) async {
        // Arrange
        mockContractListController.setMockContractData([]);

        // Act
        await tester.pumpWidget(
          TestHelpers.createTestableWidget(const LoanMainScreen()),
        );
        await TestHelpers.waitForAnimations(tester);

        // Change contract data
        final mockContracts = TestHelpers.createMockContractData();
        mockContractListController.setMockContractData(mockContracts.cast());
        await tester.pump();

        // Assert
        expect(mockContractListController.hasContracts, isTrue);
      });

      testWidgets('should handle empty contract list correctly', (WidgetTester tester) async {
        // Arrange
        mockContractListController.setMockContractData([]);
        mockContractListController.setMockContractStatusData([]);

        // Act
        await tester.pumpWidget(
          TestHelpers.createTestableWidget(const LoanMainScreen()),
        );
        await TestHelpers.waitForAnimations(tester);

        // Assert
        expect(mockContractListController.hasContracts, isFalse);
        expect(mockContractListController.hasContractStatus, isFalse);
      });
    });

    group('Error Handling Tests', () {
      testWidgets('should handle controller initialization errors gracefully', (WidgetTester tester) async {
        // Arrange
        mockContractListController.simulateApiError();

        // Act & Assert
        expect(() async {
          await tester.pumpWidget(
            TestHelpers.createTestableWidget(const LoanMainScreen()),
          );
          await TestHelpers.waitForAnimations(tester);
        }, returnsNormally);
      });
    });
  });
}
