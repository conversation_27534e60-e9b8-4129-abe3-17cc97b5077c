import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:AAMG/view/screen/loan_screen/loan_main_screen.dart';
import 'package:AAMG/controller/contract/contractlist.controller.dart';
import 'package:AAMG/controller/home/<USER>';
import 'package:AAMG/controller/profile/profile.controller.dart';
import 'package:AAMG/controller/request_loan/loan.controller.dart';
import 'package:AAMG/view/componance/widgets/button_widgets/primary_button.dart';
import '../mocks/mock_contract_list_controller.dart';
import '../mocks/mock_home_controller.dart';
import '../mocks/mock_profile_controller.dart';
import '../mocks/mock_loan_controller.dart';
import '../helpers/test_helpers.dart';

void main() {
  group('LoanMainScreen Widget Tests', () {
    late MockContractListController mockContractListController;
    late MockHomeController mockHomeController;
    late MockProfileController mockProfileController;
    late MockLoanController mockLoanController;

    setUp(() {
      TestHelpers.setupGetXForTesting();
      
      mockContractListController = MockContractListController();
      mockHomeController = MockHomeController();
      mockProfileController = MockProfileController();
      mockLoanController = MockLoanController();
      
      Get.put<ContractListController>(mockContractListController);
      Get.put<HomeController>(mockHomeController);
      Get.put<ProfileController>(mockProfileController);
      Get.put<LoanController>(mockLoanController);
    });

    tearDown(() {
      TestHelpers.cleanupGetX();
    });

    group('UI Components Tests', () {
      testWidgets('should display all main UI components', (WidgetTester tester) async {
        // Arrange
        mockContractListController.setMockContractData([]);
        mockContractListController.setMockContractStatusData([]);

        // Act
        await tester.pumpWidget(
          TestHelpers.createTestableWidget(const LoanMainScreen()),
        );
        await TestHelpers.waitForAnimations(tester);

        // Assert
        expect(find.byType(Scaffold), findsOneWidget);
        expect(find.byType(Stack), findsOneWidget);
        expect(find.byType(PrimaryButton), findsOneWidget);
        expect(find.byType(GestureDetector), findsWidgets); // Back button
      });

      testWidgets('should display header with back button and title', (WidgetTester tester) async {
        // Arrange
        mockContractListController.setMockContractData([]);

        // Act
        await tester.pumpWidget(
          TestHelpers.createTestableWidget(const LoanMainScreen()),
        );
        await TestHelpers.waitForAnimations(tester);

        // Assert
        expect(find.byType(SvgPicture), findsWidgets); // Back button icon
        expect(find.byType(Text), findsWidgets); // Title text
        expect(find.byType(Container), findsWidgets); // Header container
      });

      testWidgets('should display primary button with correct properties', (WidgetTester tester) async {
        // Arrange
        mockContractListController.setMockContractData([]);

        // Act
        await tester.pumpWidget(
          TestHelpers.createTestableWidget(const LoanMainScreen()),
        );
        await TestHelpers.waitForAnimations(tester);

        // Assert
        final primaryButton = tester.widget<PrimaryButton>(find.byType(PrimaryButton));
        expect(primaryButton.isActive, isTrue);
        expect(primaryButton.onPressed, isNotNull);
      });
    });

    group('Loan Default View Tests', () {
      testWidgets('should display loan default image and text when no contracts', (WidgetTester tester) async {
        // Arrange
        mockContractListController.setMockContractData([]);
        mockContractListController.setMockContractStatusData([]);

        // Act
        await tester.pumpWidget(
          TestHelpers.createTestableWidget(const LoanMainScreen()),
        );
        await TestHelpers.waitForAnimations(tester);

        // Assert
        expect(find.byType(Image), findsOneWidget); // Loan default image
        expect(find.byType(Column), findsWidgets); // Layout structure
        expect(find.byType(Container), findsWidgets); // Image container
      });

      testWidgets('should display correct loan default text content', (WidgetTester tester) async {
        // Arrange
        mockContractListController.setMockContractData([]);
        mockContractListController.setMockContractStatusData([]);

        // Act
        await tester.pumpWidget(
          TestHelpers.createTestableWidget(const LoanMainScreen()),
        );
        await TestHelpers.waitForAnimations(tester);

        // Assert
        expect(find.byType(Text), findsWidgets);
        expect(find.byType(RichText), findsWidgets); // Rich text for description
      });

      testWidgets('should center loan default content properly', (WidgetTester tester) async {
        // Arrange
        mockContractListController.setMockContractData([]);
        mockContractListController.setMockContractStatusData([]);

        // Act
        await tester.pumpWidget(
          TestHelpers.createTestableWidget(const LoanMainScreen()),
        );
        await TestHelpers.waitForAnimations(tester);

        // Assert
        expect(find.byType(Row), findsWidgets); // Row with MainAxisAlignment.center
        expect(find.byType(Center), findsWidgets); // Center widget
      });
    });

    group('Loan Request View Tests', () {
      testWidgets('should display ListView when has contract data', (WidgetTester tester) async {
        // Arrange
        final mockContracts = TestHelpers.createMockContractData();
        mockContractListController.setMockContractData(mockContracts.cast());

        // Act
        await tester.pumpWidget(
          TestHelpers.createTestableWidget(const LoanMainScreen()),
        );
        await TestHelpers.waitForAnimations(tester);

        // Assert
        expect(find.byType(ListView), findsOneWidget);
        expect(find.byType(SingleChildScrollView), findsOneWidget);
      });

      testWidgets('should display contract cards with correct structure', (WidgetTester tester) async {
        // Arrange
        final mockContracts = TestHelpers.createMockContractData();
        mockContractListController.setMockContractData(mockContracts.cast());

        // Act
        await tester.pumpWidget(
          TestHelpers.createTestableWidget(const LoanMainScreen()),
        );
        await TestHelpers.waitForAnimations(tester);

        // Assert
        expect(find.byType(Container), findsWidgets); // Contract cards
        expect(find.byType(Column), findsWidgets); // Card layout
      });

      testWidgets('should display pending loan cards when has contract status', (WidgetTester tester) async {
        // Arrange
        final mockStatuses = TestHelpers.createMockContractStatusData();
        mockContractListController.setMockContractStatusData(mockStatuses.cast());

        // Act
        await tester.pumpWidget(
          TestHelpers.createTestableWidget(const LoanMainScreen()),
        );
        await TestHelpers.waitForAnimations(tester);

        // Assert
        expect(find.byType(ListView), findsWidgets); // Pending loan list
        expect(find.byType(InkWell), findsWidgets); // Clickable status cards
      });
    });

    group('Primary Button Interaction Tests', () {
      testWidgets('should respond to primary button tap', (WidgetTester tester) async {
        // Arrange
        mockContractListController.setMockContractData([]);
        mockHomeController.setMockGuestStatus(false);
        mockProfileController.setMockCompleteAddress(true);

        // Act
        await tester.pumpWidget(
          TestHelpers.createTestableWidget(const LoanMainScreen()),
        );
        await TestHelpers.waitForAnimations(tester);

        // Tap primary button
        await TestHelpers.tapAndWait(tester, find.byType(PrimaryButton));

        // Assert
        // Note: ต้องเพิ่มการตรวจสอบ callback execution
        expect(find.byType(PrimaryButton), findsOneWidget);
      });

      testWidgets('should handle guest user button tap', (WidgetTester tester) async {
        // Arrange
        mockContractListController.setMockContractData([]);
        mockHomeController.setMockGuestStatus(true);

        // Act
        await tester.pumpWidget(
          TestHelpers.createTestableWidget(const LoanMainScreen()),
        );
        await TestHelpers.waitForAnimations(tester);

        // Tap primary button
        await TestHelpers.tapAndWait(tester, find.byType(PrimaryButton));

        // Assert
        expect(mockHomeController.isGuest!.value, isTrue);
      });

      testWidgets('should handle incomplete profile button tap', (WidgetTester tester) async {
        // Arrange
        mockContractListController.setMockContractData([]);
        mockHomeController.setMockGuestStatus(false);
        mockProfileController.setMockCompleteAddress(false);

        // Act
        await tester.pumpWidget(
          TestHelpers.createTestableWidget(const LoanMainScreen()),
        );
        await TestHelpers.waitForAnimations(tester);

        // Tap primary button
        await TestHelpers.tapAndWait(tester, find.byType(PrimaryButton));

        // Assert
        expect(mockProfileController.hasCompleteAddress, isFalse);
      });
    });

    group('Navigation Tests', () {
      testWidgets('should handle back button tap', (WidgetTester tester) async {
        // Arrange
        mockContractListController.setMockContractData([]);

        // Act
        await tester.pumpWidget(
          TestHelpers.createTestableWidget(const LoanMainScreen()),
        );
        await TestHelpers.waitForAnimations(tester);

        // Find back button (first GestureDetector)
        final backButton = find.byType(GestureDetector).first;
        await TestHelpers.tapAndWait(tester, backButton);

        // Assert
        expect(mockLoanController.wasResetCalled, isTrue);
      });

      testWidgets('should display information icon in header', (WidgetTester tester) async {
        // Arrange
        mockContractListController.setMockContractData([]);

        // Act
        await tester.pumpWidget(
          TestHelpers.createTestableWidget(const LoanMainScreen()),
        );
        await TestHelpers.waitForAnimations(tester);

        // Assert
        expect(find.byType(Container), findsWidgets); // Information icon container
        // Note: ต้องเพิ่มการตรวจสอบ DecorationImage
      });
    });

    group('Responsive Design Tests', () {
      testWidgets('should adapt to different screen sizes', (WidgetTester tester) async {
        // Arrange
        mockContractListController.setMockContractData([]);

        // Act
        await tester.pumpWidget(
          TestHelpers.createTestableWidget(const LoanMainScreen()),
        );
        await TestHelpers.waitForAnimations(tester);

        // Assert
        expect(find.byType(LoanMainScreen), findsOneWidget);
        // Note: ต้องเพิ่มการทดสอบ ScreenUtil responsive behavior
      });

      testWidgets('should handle different contract list sizes', (WidgetTester tester) async {
        // Arrange - Large contract list
        final largeContractList = List.generate(10, (index) => {
          'ctt_code': 'AAM00${index}DT00${index}',
          'guarantee_type': '${(index % 3) + 1}',
          'loan_amount': '${(index + 1) * 100000}',
          'loan_periods': '${(index + 1) * 6}',
        });
        mockContractListController.setMockContractData(largeContractList.cast());

        // Act
        await tester.pumpWidget(
          TestHelpers.createTestableWidget(const LoanMainScreen()),
        );
        await TestHelpers.waitForAnimations(tester);

        // Assert
        expect(find.byType(ListView), findsOneWidget);
        expect(mockContractListController.contractCount, equals(10));
      });
    });

    group('State Update Tests', () {
      testWidgets('should update UI when switching between default and request views', (WidgetTester tester) async {
        // Arrange - Start with empty contracts
        mockContractListController.setMockContractData([]);

        // Act
        await tester.pumpWidget(
          TestHelpers.createTestableWidget(const LoanMainScreen()),
        );
        await TestHelpers.waitForAnimations(tester);

        // Verify default view
        expect(find.byType(Image), findsOneWidget);

        // Add contracts and update
        final mockContracts = TestHelpers.createMockContractData();
        mockContractListController.setMockContractData(mockContracts.cast());
        await tester.pump();

        // Assert - Should show request view
        expect(mockContractListController.hasContracts, isTrue);
      });
    });
  });
}
