import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:AAMG/view/screen/branch/branch_screen.dart';
import 'package:AAMG/controller/branch/branch.controller.dart';
import 'package:AAMG/view/componance/widgets/header_widgets/header_general.dart';
import '../mocks/mock_branch_controller.dart';
import '../helpers/test_helpers.dart';

void main() {
  group('BranchScreen Widget Tests', () {
    late MockBranchController mockController;

    setUp(() {
      TestHelpers.setupGetXForTesting();
      mockController = MockBranchController();
      Get.put<BranchController>(mockController);
    });

    tearDown(() {
      TestHelpers.cleanupGetX();
    });

    group('UI Components Tests', () {
      testWidgets('should display all main UI components', (WidgetTester tester) async {
        // Arrange
        await mockController.getBranchData();

        // Act
        await tester.pumpWidget(
          TestHelpers.createTestableWidget(BranchScreen()),
        );
        await TestHelpers.waitForAnimations(tester);

        // Assert
        expect(find.byType(Scaffold), findsOneWidget);
        expect(find.byType(RefreshIndicator), findsOneWidget);
        expect(find.byType(Stack), findsOneWidget);
        expect(find.byType(HeaderGeneral), findsOneWidget);
        expect(find.byType(TextFormField), findsOneWidget);
        expect(find.byType(ListView), findsOneWidget);
      });

      testWidgets('should display search field with correct properties', (WidgetTester tester) async {
        // Arrange
        await mockController.getBranchData();

        // Act
        await tester.pumpWidget(
          TestHelpers.createTestableWidget(BranchScreen()),
        );
        await TestHelpers.waitForAnimations(tester);

        // Assert
        final searchField = tester.widget<TextFormField>(find.byType(TextFormField));
        expect(searchField.keyboardType, equals(TextInputType.text));
        expect(searchField.decoration?.filled, isTrue);
        expect(searchField.decoration?.fillColor, equals(const Color(0xFFF9F9F9)));
        
        // ตรวจสอบ prefixIcon
        expect(searchField.decoration?.prefixIcon, isNotNull);
      });

      testWidgets('should display search icon in search field', (WidgetTester tester) async {
        // Arrange
        await mockController.getBranchData();

        // Act
        await tester.pumpWidget(
          TestHelpers.createTestableWidget(BranchScreen()),
        );
        await TestHelpers.waitForAnimations(tester);

        // Assert
        expect(find.byType(SvgPicture), findsWidgets);
        // Note: ต้องตรวจสอบ SvgPicture ที่เป็น search icon โดยเฉพาะ
      });

      testWidgets('should display header with back button', (WidgetTester tester) async {
        // Arrange
        await mockController.getBranchData();

        // Act
        await tester.pumpWidget(
          TestHelpers.createTestableWidget(BranchScreen()),
        );
        await TestHelpers.waitForAnimations(tester);

        // Assert
        expect(find.byType(HeaderGeneral), findsOneWidget);
        
        final header = tester.widget<HeaderGeneral>(find.byType(HeaderGeneral));
        expect(header.firstIcon, isNotNull);
        expect(header.firstOnPressed, isNotNull);
      });
    });

    group('Search Field Interaction Tests', () {
      testWidgets('should focus search field when tapped', (WidgetTester tester) async {
        // Arrange
        await mockController.getBranchData();
        await tester.pumpWidget(
          TestHelpers.createTestableWidget(BranchScreen()),
        );
        await TestHelpers.waitForAnimations(tester);

        // Act
        await TestHelpers.tapAndWait(tester, find.byType(TextFormField));

        // Assert
        expect(mockController.isActiveSearchZone.value, isTrue);
      });

      testWidgets('should accept text input in search field', (WidgetTester tester) async {
        // Arrange
        await mockController.getBranchData();
        await tester.pumpWidget(
          TestHelpers.createTestableWidget(BranchScreen()),
        );
        await TestHelpers.waitForAnimations(tester);

        // Act
        await TestHelpers.enterTextAndWait(tester, find.byType(TextFormField), 'ทดสอบการค้นหา');

        // Assert
        expect(find.text('ทดสอบการค้นหา'), findsOneWidget);
      });

      testWidgets('should trigger search when entering 3+ characters', (WidgetTester tester) async {
        // Arrange
        await mockController.getBranchData();
        await tester.pumpWidget(
          TestHelpers.createTestableWidget(BranchScreen()),
        );
        await TestHelpers.waitForAnimations(tester);

        // Act
        await TestHelpers.enterTextAndWait(tester, find.byType(TextFormField), 'ทดสอบ');

        // Assert
        expect(mockController.getLastSearchKeyword, equals('ทดสอบ'));
      });

      testWidgets('should not trigger search with less than 3 characters', (WidgetTester tester) async {
        // Arrange
        await mockController.getBranchData();
        await tester.pumpWidget(
          TestHelpers.createTestableWidget(BranchScreen()),
        );
        await TestHelpers.waitForAnimations(tester);

        // Act
        await TestHelpers.enterTextAndWait(tester, find.byType(TextFormField), 'ab');

        // Assert
        expect(mockController.getLastSearchKeyword, isNull);
      });

      testWidgets('should reset data when clearing search field', (WidgetTester tester) async {
        // Arrange
        await mockController.getBranchData();
        await tester.pumpWidget(
          TestHelpers.createTestableWidget(BranchScreen()),
        );
        await TestHelpers.waitForAnimations(tester);

        // Act - Search first
        await TestHelpers.enterTextAndWait(tester, find.byType(TextFormField), 'ทดสอบ');
        // Then clear
        await TestHelpers.enterTextAndWait(tester, find.byType(TextFormField), '');

        // Assert
        expect(mockController.branchDataList!.length, equals(mockController.branchInitList!.length));
      });
    });

    group('Branch List Display Tests', () {
      testWidgets('should display ListView for branch data', (WidgetTester tester) async {
        // Arrange
        await mockController.getBranchData();

        // Act
        await tester.pumpWidget(
          TestHelpers.createTestableWidget(BranchScreen()),
        );
        await TestHelpers.waitForAnimations(tester);

        // Assert
        expect(find.byType(ListView), findsOneWidget);
        
        final listView = tester.widget<ListView>(find.byType(ListView));
        expect(listView.padding, equals(EdgeInsets.zero));
      });

      testWidgets('should display branch items when data is available', (WidgetTester tester) async {
        // Arrange
        final mockData = TestHelpers.createMockBranchData();
        mockController.setMockData(mockData);

        // Act
        await tester.pumpWidget(
          TestHelpers.createTestableWidget(BranchScreen()),
        );
        await TestHelpers.waitForAnimations(tester);

        // Assert
        expect(find.byType(ListView), findsOneWidget);
        expect(mockController.dataCount, equals(mockData.length));
      });

      testWidgets('should handle empty branch list', (WidgetTester tester) async {
        // Arrange
        mockController.setMockData([]);

        // Act
        await tester.pumpWidget(
          TestHelpers.createTestableWidget(BranchScreen()),
        );
        await TestHelpers.waitForAnimations(tester);

        // Assert
        expect(find.byType(ListView), findsOneWidget);
        expect(mockController.dataCount, equals(0));
      });
    });

    group('Refresh Functionality Tests', () {
      testWidgets('should trigger refresh when pulling down', (WidgetTester tester) async {
        // Arrange
        await mockController.getBranchData();
        await tester.pumpWidget(
          TestHelpers.createTestableWidget(BranchScreen()),
        );
        await TestHelpers.waitForAnimations(tester);

        // Act
        await tester.fling(find.byType(RefreshIndicator), const Offset(0, 300), 1000);
        await TestHelpers.waitForAnimations(tester);

        // Assert
        expect(mockController.selectedZone.value, equals(''));
        expect(mockController.isActiveSelectZone.value, isFalse);
        expect(mockController.isActiveSearchZone.value, isFalse);
      });

      testWidgets('should show refresh indicator during refresh', (WidgetTester tester) async {
        // Arrange
        await mockController.getBranchData();
        await tester.pumpWidget(
          TestHelpers.createTestableWidget(BranchScreen()),
        );
        await TestHelpers.waitForAnimations(tester);

        // Act
        await tester.fling(find.byType(RefreshIndicator), const Offset(0, 300), 1000);
        await tester.pump(); // Don't wait for animations to see loading state

        // Assert
        expect(find.byType(RefreshIndicator), findsOneWidget);
      });
    });

    group('Responsive Design Tests', () {
      testWidgets('should adapt to different screen sizes', (WidgetTester tester) async {
        // Arrange
        await mockController.getBranchData();

        // Act
        await tester.pumpWidget(
          TestHelpers.createTestableWidget(BranchScreen()),
        );
        await TestHelpers.waitForAnimations(tester);

        // Assert
        // ตรวจสอบว่า UI components ใช้ ScreenUtil สำหรับ responsive design
        expect(find.byType(BranchScreen), findsOneWidget);
        
        // Note: ต้องเพิ่มการทดสอบ responsive behavior เพิ่มเติม
      });
    });

    group('Accessibility Tests', () {
      testWidgets('should have proper accessibility labels', (WidgetTester tester) async {
        // Arrange
        await mockController.getBranchData();

        // Act
        await tester.pumpWidget(
          TestHelpers.createTestableWidget(BranchScreen()),
        );
        await TestHelpers.waitForAnimations(tester);

        // Assert
        // Note: ต้องเพิ่ม Semantics labels ใน UI components
        expect(find.byType(BranchScreen), findsOneWidget);
      });
    });
  });
}
