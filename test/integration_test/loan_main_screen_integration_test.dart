import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:AAMG/controller/contract/contractlist.controller.dart';
import 'package:AAMG/controller/home/<USER>';
import 'package:AAMG/controller/profile/profile.controller.dart';
import 'package:AAMG/controller/request_loan/loan.controller.dart';
import '../mocks/mock_contract_list_controller.dart';
import '../mocks/mock_home_controller.dart';
import '../mocks/mock_profile_controller.dart';
import '../mocks/mock_loan_controller.dart';
import '../helpers/test_helpers.dart';

void main() {
  group('LoanMainScreen Integration Tests', () {
    late MockContractListController contractListController;
    late MockHomeController homeController;
    late MockProfileController profileController;
    late MockLoanController loanController;

    setUp(() {
      TestHelpers.setupGetXForTesting();
      
      contractListController = MockContractListController();
      homeController = MockHomeController();
      profileController = MockProfileController();
      loanController = MockLoanController();
      
      Get.put<ContractListController>(contractListController);
      Get.put<HomeController>(homeController);
      Get.put<ProfileController>(profileController);
      Get.put<LoanController>(loanController);
    });

    tearDown() {
      TestHelpers.cleanupGetX();
    });

    group('Contract Data Management Tests', () {
      test('should load contract data successfully', () async {
        // Arrange
        expect(contractListController.contractList.isEmpty, isTrue);

        // Act
        await contractListController.customerContactList();

        // Assert
        expect(contractListController.hasContracts, isTrue);
        expect(contractListController.chk_contract!.value, isTrue);
      });

      test('should handle empty contract data', () async {
        // Arrange
        contractListController.setMockContractData([]);

        // Act
        await contractListController.customerContactList();

        // Assert
        expect(contractListController.hasContracts, isFalse);
        expect(contractListController.contractCount, equals(0));
      });

      test('should handle contract data loading error', () async {
        // Arrange
        contractListController.simulateApiError();

        // Act & Assert
        expect(() async => await contractListController.customerContactList(), 
               throwsException);
      });

      test('should format currency correctly', () {
        // Arrange
        const testAmount = '1000000';

        // Act
        final formatted = contractListController.formatCurrency(testAmount);

        // Assert
        expect(formatted, equals('1,000,000'));
      });

      test('should get guarantee type name correctly', () {
        // Arrange
        final mockContracts = TestHelpers.createMockContractData();
        contractListController.setMockContractData(mockContracts.cast());

        // Act
        final guaranteeName = contractListController.getGuaranteeTypeName(0);

        // Assert
        expect(guaranteeName, isNotEmpty);
        expect(guaranteeName, equals('รถยนต์')); // Based on guarantee_type '1'
      });
    });

    group('Contract Status Management Tests', () {
      test('should load contract status successfully', () async {
        // Arrange
        expect(contractListController.contractStatus.isEmpty, isTrue);

        // Act
        final result = await contractListController.getContactStatus();

        // Assert
        expect(result, isTrue);
        expect(contractListController.hasContractStatus, isTrue);
      });

      test('should handle empty contract status', () async {
        // Arrange
        contractListController.setMockContractStatusData([]);

        // Act
        final result = await contractListController.getContactStatus();

        // Assert
        expect(result, isFalse);
        expect(contractListController.hasContractStatus, isFalse);
      });

      test('should select contract status correctly', () async {
        // Arrange
        final mockStatuses = TestHelpers.createMockContractStatusData();
        contractListController.setMockContractStatusData(mockStatuses.cast());
        const testIndex = 1;

        // Act
        await contractListController.selectedStatusContract(testIndex);

        // Assert
        expect(contractListController.selectedStatusContractIndex!.value, equals(testIndex));
        expect(contractListController.getLastSelectedContractIndex, equals(testIndex.toString()));
      });
    });

    group('User State Management Tests', () {
      test('should handle guest user state correctly', () {
        // Arrange
        expect(homeController.isGuest!.value, isFalse);

        // Act
        homeController.setMockGuestStatus(true);

        // Assert
        expect(homeController.isGuest!.value, isTrue);
        expect(homeController.getMockIsGuest, isTrue);
      });

      test('should handle registered user state correctly', () {
        // Arrange
        homeController.setMockGuestStatus(true);

        // Act
        homeController.setMockGuestStatus(false);

        // Assert
        expect(homeController.isGuest!.value, isFalse);
        expect(homeController.getMockIsGuest, isFalse);
      });

      test('should manage loan data visibility', () {
        // Arrange
        expect(homeController.showLoanData!.value, isFalse);

        // Act
        homeController.setMockShowLoanData(true);

        // Assert
        expect(homeController.showLoanData!.value, isTrue);
        expect(homeController.getMockShowLoanData, isTrue);
      });
    });

    group('Profile Management Tests', () {
      test('should validate complete address', () {
        // Arrange
        profileController.setMockCompleteAddress(true);

        // Act
        final hasCompleteAddress = profileController.hasCompleteAddress;

        // Assert
        expect(hasCompleteAddress, isTrue);
        expect(profileController.profile.value.amphur, isNotNull);
        expect(profileController.profile.value.province, isNotNull);
      });

      test('should validate incomplete address', () {
        // Arrange
        profileController.setMockCompleteAddress(false);

        // Act
        final hasCompleteAddress = profileController.hasCompleteAddress;

        // Assert
        expect(hasCompleteAddress, isFalse);
        expect(profileController.profile.value.amphur, isNull);
        expect(profileController.profile.value.province, isNull);
      });

      test('should reset profile data correctly', () {
        // Arrange
        profileController.setMockCompleteAddress(false);

        // Act
        profileController.resetMockProfile();

        // Assert
        expect(profileController.hasCompleteAddress, isTrue);
        expect(profileController.getMockHasCompleteAddress, isTrue);
      });
    });

    group('Loan Controller Integration Tests', () {
      test('should reset accept term policy correctly', () async {
        // Arrange
        loanController.setMockLoanData(acceptedPolicy: true);
        expect(loanController.isAcceptedTermPolicy!.value, isTrue);

        // Act
        await loanController.resetAcceptTermPolicy();

        // Assert
        expect(loanController.isAcceptedTermPolicy!.value, isFalse);
        expect(loanController.wasResetCalled, isTrue);
      });

      test('should handle loan amount calculation', () {
        // Arrange
        loanController.setMockLoanData(amount: '100000');

        // Act
        loanController.calculateLoan(50000, 'plus');

        // Assert
        expect(loanController.hasLoanAmount, isTrue);
        expect(loanController.loan_amount.value.text, contains('150,000'));
      });

      test('should select guarantee type correctly', () {
        // Arrange
        const guarantee = 'รถยนต์';
        const index = 0;

        // Act
        loanController.selectGuarantee(guarantee, index);

        // Assert
        expect(loanController.selectedGuarantee.value, equals(guarantee));
        expect(loanController.selectedGuaranteeIndex.value, equals(index));
        expect(loanController.hasSelectedGuarantee, isTrue);
      });

      test('should select period correctly', () {
        // Arrange
        const period = '24';
        const index = 3;

        // Act
        loanController.selectPeriod(period, index);

        // Assert
        expect(loanController.selectedPeriod.value, equals(period));
        expect(loanController.selectedPeriodIndex.value, equals(index));
        expect(loanController.hasSelectedPeriod, isTrue);
      });
    });

    group('Business Logic Integration Tests', () {
      test('should handle complete loan application flow for guest user', () {
        // Arrange
        homeController.setMockGuestStatus(true);
        profileController.setMockCompleteAddress(true);

        // Act & Assert
        expect(homeController.isGuest!.value, isTrue);
        // Guest user should be redirected to registration
      });

      test('should handle complete loan application flow for user with incomplete profile', () {
        // Arrange
        homeController.setMockGuestStatus(false);
        profileController.setMockCompleteAddress(false);

        // Act & Assert
        expect(homeController.isGuest!.value, isFalse);
        expect(profileController.hasCompleteAddress, isFalse);
        // User should be prompted to update address
      });

      test('should handle complete loan application flow for user with complete profile', () {
        // Arrange
        homeController.setMockGuestStatus(false);
        profileController.setMockCompleteAddress(true);

        // Act & Assert
        expect(homeController.isGuest!.value, isFalse);
        expect(profileController.hasCompleteAddress, isTrue);
        // User should proceed to loan agreement
      });

      test('should handle contract list and status integration', () async {
        // Arrange
        final mockContracts = TestHelpers.createMockContractData();
        final mockStatuses = TestHelpers.createMockContractStatusData();

        // Act
        contractListController.setMockContractData(mockContracts.cast());
        contractListController.setMockContractStatusData(mockStatuses.cast());

        // Assert
        expect(contractListController.hasContracts, isTrue);
        expect(contractListController.hasContractStatus, isTrue);
        expect(contractListController.contractCount, equals(mockContracts.length));
        expect(contractListController.contractStatusCount, equals(mockStatuses.length));
      });
    });

    group('Error Handling Integration Tests', () {
      test('should handle multiple controller errors gracefully', () {
        // Arrange
        contractListController.simulateApiError();
        loanController.simulateRequestLoanError();

        // Act & Assert
        expect(() async => await contractListController.customerContactList(), 
               throwsException);
        expect(() async => await loanController.requestLoan(null), 
               throwsException);
      });

      test('should recover from errors correctly', () {
        // Arrange
        contractListController.simulateApiError();

        // Act
        contractListController.resetApiError();

        // Assert
        expect(() async => await contractListController.customerContactList(), 
               returnsNormally);
      });
    });

    group('Data Consistency Tests', () {
      test('should maintain data consistency across controllers', () {
        // Arrange
        homeController.setMockGuestStatus(false);
        profileController.setMockCompleteAddress(true);
        loanController.setMockLoanData(acceptedPolicy: true);

        // Act
        final isGuest = homeController.isGuest!.value;
        final hasCompleteAddress = profileController.hasCompleteAddress;
        final acceptedPolicy = loanController.isAcceptedTermPolicy!.value;

        // Assert
        expect(isGuest, isFalse);
        expect(hasCompleteAddress, isTrue);
        expect(acceptedPolicy, isTrue);
        // All conditions met for loan application
      });

      test('should reset all controllers correctly', () {
        // Arrange
        homeController.setMockGuestStatus(true);
        profileController.setMockCompleteAddress(false);
        loanController.setMockLoanData(acceptedPolicy: true);

        // Act
        homeController.resetMockData();
        profileController.resetMockProfile();
        loanController.resetMockData();

        // Assert
        expect(homeController.isGuest!.value, isFalse);
        expect(profileController.hasCompleteAddress, isTrue);
        expect(loanController.isAcceptedTermPolicy!.value, isFalse);
      });
    });
  });
}
