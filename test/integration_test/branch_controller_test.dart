import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:AAMG/models/branch/branch_model.dart';
import '../mocks/mock_branch_controller.dart';
import '../mocks/mock_http_service.dart';
import '../helpers/test_helpers.dart';

void main() {
  group('BranchController Integration Tests', () {
    late MockBranchController controller;

    setUp(() {
      TestHelpers.setupGetXForTesting();
      controller = MockBranchController();
      MockHttpService.reset();
    });

    tearDown(() {
      TestHelpers.cleanupGetX();
      MockHttpService.reset();
    });

    group('getBranchData Tests', () {
      test('should load branch data successfully', () async {
        // Arrange
        expect(controller.branchDataList!.isEmpty, isTrue);

        // Act
        await controller.getBranchData();

        // Assert
        expect(controller.branchDataList!.isNotEmpty, isTrue);
        expect(controller.branchInitList!.isNotEmpty, isTrue);
        expect(controller.branchDataList!.length, equals(controller.branchInitList!.length));
      });

      test('should clear existing data before loading new data', () async {
        // Arrange
        controller.setMockData(TestHelpers.createMockBranchData());
        expect(controller.branchDataList!.isNotEmpty, isTrue);

        // Act
        await controller.getBranchData();

        // Assert
        expect(controller.branchDataList!.isNotEmpty, isTrue);
        // ข้อมูลใหม่ควรถูกโหลดเข้ามา
      });

      test('should handle API error gracefully', () async {
        // Arrange
        controller.simulateApiError();

        // Act & Assert
        expect(() async => await controller.getBranchData(), throwsException);
      });

      test('should set loading state correctly', () async {
        // Arrange
        expect(controller.getIsLoading, isFalse);

        // Act
        final future = controller.getBranchData();
        
        // Assert - during loading
        expect(controller.getIsLoading, isTrue);
        
        await future;
        
        // Assert - after loading
        expect(controller.getIsLoading, isFalse);
      });
    });

    group('searchBranchData Tests', () {
      test('should search branches by keyword successfully', () async {
        // Arrange
        await controller.getBranchData();
        final keyword = 'ทดสอบ';

        // Act
        await controller.searchBranchData(keyword);

        // Assert
        expect(controller.getLastSearchKeyword, equals(keyword));
        expect(controller.branchDataList!.isNotEmpty, isTrue);
        
        // ตรวจสอบว่าผลลัพธ์มีคำค้นหา
        final hasMatchingResult = controller.branchDataList!.any((branch) =>
            branch.branchName.toLowerCase().contains(keyword.toLowerCase()) ||
            branch.province.toLowerCase().contains(keyword.toLowerCase()) ||
            branch.zone.toLowerCase().contains(keyword.toLowerCase()));
        expect(hasMatchingResult, isTrue);
      });

      test('should return filtered results based on keyword', () async {
        // Arrange
        await controller.getBranchData();
        final originalCount = controller.branchDataList!.length;

        // Act
        await controller.searchBranchData('กรุงเทพ');

        // Assert
        expect(controller.branchDataList!.length, lessThanOrEqualTo(originalCount));
        
        // ตรวจสอบว่าผลลัพธ์ทั้งหมดมีคำค้นหา
        final allResultsMatch = controller.branchDataList!.every((branch) =>
            branch.branchName.toLowerCase().contains('กรุงเทพ'.toLowerCase()) ||
            branch.province.toLowerCase().contains('กรุงเทพ'.toLowerCase()) ||
            branch.zone.toLowerCase().contains('กรุงเทพ'.toLowerCase()));
        expect(allResultsMatch, isTrue);
      });

      test('should handle search API error and reset to initial data', () async {
        // Arrange
        await controller.getBranchData();
        final initialCount = controller.branchDataList!.length;
        controller.simulateApiError();

        // Act & Assert
        expect(() async => await controller.searchBranchData('test'), throwsException);
        
        // ข้อมูลควรถูกรีเซ็ตกลับเป็นข้อมูลเริ่มต้น
        expect(controller.branchDataList!.length, equals(initialCount));
      });

      test('should clear data list before searching', () async {
        // Arrange
        await controller.getBranchData();
        expect(controller.branchDataList!.isNotEmpty, isTrue);

        // Act
        final future = controller.searchBranchData('test');
        
        // Assert - during search, data should be cleared
        // Note: ในการทดสอบจริง อาจต้องใช้ Timer หรือ callback เพื่อตรวจสอบ state ระหว่างการค้นหา
        
        await future;
        expect(controller.branchDataList!.isNotEmpty, isTrue);
      });
    });

    group('sortBranchDataByZone Tests', () {
      test('should filter branches by selected zone', () async {
        // Arrange
        await controller.getBranchData();
        controller.setSelectedZone('กลาง');

        // Act
        await controller.sortBranchDataByZone();

        // Assert
        expect(controller.selectedZone.value, equals('กลาง'));
        
        // ตรวจสอบว่าผลลัพธ์ทั้งหมดเป็น zone ที่เลือก
        final allResultsMatchZone = controller.branchDataList!.every(
          (branch) => branch.zone == 'กลาง'
        );
        expect(allResultsMatchZone, isTrue);
      });

      test('should return empty list when no branches match selected zone', () async {
        // Arrange
        await controller.getBranchData();
        controller.setSelectedZone('ไม่มีโซนนี้');

        // Act
        await controller.sortBranchDataByZone();

        // Assert
        expect(controller.branchDataList!.isEmpty, isTrue);
      });

      test('should handle empty initial data', () async {
        // Arrange
        controller.branchInitList!.clear();
        controller.setSelectedZone('กลาง');

        // Act
        await controller.sortBranchDataByZone();

        // Assert
        expect(controller.branchDataList!.isEmpty, isTrue);
      });
    });

    group('setIntialBranchData Tests', () {
      test('should restore initial data when available', () async {
        // Arrange
        await controller.getBranchData();
        final initialCount = controller.branchInitList!.length;
        
        // Clear current data
        controller.branchDataList!.clear();
        expect(controller.branchDataList!.isEmpty, isTrue);

        // Act
        await controller.setIntialBranchData();

        // Assert
        expect(controller.branchDataList!.length, equals(initialCount));
        expect(controller.branchDataList!.length, equals(controller.branchInitList!.length));
      });

      test('should call getBranchData when initial data is empty', () async {
        // Arrange
        controller.branchInitList!.clear();
        controller.branchDataList!.clear();

        // Act
        await controller.setIntialBranchData();

        // Assert
        expect(controller.branchDataList!.isNotEmpty, isTrue);
        expect(controller.branchInitList!.isNotEmpty, isTrue);
      });
    });

    group('State Management Tests', () {
      test('should manage search zone activation correctly', () {
        // Arrange
        expect(controller.isActiveSearchZone.value, isFalse);

        // Act
        controller.setActivateSearchZone(true);

        // Assert
        expect(controller.isActiveSearchZone.value, isTrue);

        // Act
        controller.setActivateSearchZone(false);

        // Assert
        expect(controller.isActiveSearchZone.value, isFalse);
      });

      test('should manage select zone activation correctly', () {
        // Arrange
        expect(controller.isActiveSelectZone.value, isFalse);

        // Act
        controller.setActivateSelectZone(true);

        // Assert
        expect(controller.isActiveSelectZone.value, isTrue);

        // Act
        controller.setActivateSelectZone(false);

        // Assert
        expect(controller.isActiveSelectZone.value, isFalse);
      });

      test('should set selected zone and trigger sorting', () async {
        // Arrange
        await controller.getBranchData();
        expect(controller.selectedZone.value, equals(''));

        // Act
        controller.setSelectedZone('เหนือ');

        // Assert
        expect(controller.selectedZone.value, equals('เหนือ'));
        expect(controller.getLastSelectedZone, equals('เหนือ'));
        
        // ตรวจสอบว่าข้อมูลถูกกรองแล้ว
        final allResultsMatchZone = controller.branchDataList!.every(
          (branch) => branch.zone == 'เหนือ'
        );
        expect(allResultsMatchZone, isTrue);
      });

      test('should clear all config data correctly', () async {
        // Arrange
        await controller.getBranchData();
        controller.setSelectedZone('กลาง');
        controller.setActivateSearchZone(true);
        controller.setActivateSelectZone(true);
        controller.searchController.value.text = 'test search';

        // Act
        controller.clearConfigData();

        // Assert
        expect(controller.selectedZone.value, equals(''));
        expect(controller.isActiveSearchZone.value, isFalse);
        expect(controller.isActiveSelectZone.value, isFalse);
        expect(controller.itemsToShow.value, equals(10));
        expect(controller.searchController.value.text, equals(''));
        expect(controller.getLastSearchKeyword, isNull);
        expect(controller.getLastSelectedZone, isNull);
      });
    });
  });
}
