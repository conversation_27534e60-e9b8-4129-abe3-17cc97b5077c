import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:AAMG/models/branch/branch_model.dart';

/// Helper functions สำหรับการทดสอบ
class TestHelpers {
  /// สร้าง MaterialApp wrapper สำหรับ widget testing
  static Widget createTestableWidget(Widget child) {
    return ScreenUtilInit(
      designSize: const Size(375, 812),
      minTextAdapt: true,
      splitScreenMode: true,
      builder: (context, child) {
        return GetMaterialApp(
          home: child,
          locale: const Locale('th', 'TH'),
          fallbackLocale: const Locale('en', 'US'),
        );
      },
      child: child,
    );
  }

  /// สร้าง mock branch data สำหรับการทดสอบ
  static List<Branch> createMockBranchData() {
    return [
      Branch(
        branchName: 'สาขาทดสอบ 1',
        branchNameTh: 'สาขาทดสอบ 1',
        branchNameEn: 'Test Branch 1',
        branchId: 'TEST001',
        province: 'กรุงเทพมหานคร',
        zone: 'กลาง',
        address: '123 ถนนทดสอบ',
        addressTh: '123 ถนนทดสอบ',
        addressEn: '123 Test Road',
        addressDesc: 'ใกล้ BTS สถานีทดสอบ',
        branchPhone: '02-123-4567',
        lineId: '@testbranch1',
        facebookId: 'testbranch1',
        facebookUrl: 'https://facebook.com/testbranch1',
        telegram_id: '@testbranch1',
        whatsapp_id: '66812345678',
        mapUrl: 'https://maps.google.com/test1',
        latitude: '13.7563',
        longitude: '100.5018',
      ),
      Branch(
        branchName: 'สาขาทดสอบ 2',
        branchNameTh: 'สาขาทดสอบ 2',
        branchNameEn: 'Test Branch 2',
        branchId: 'TEST002',
        province: 'เชียงใหม่',
        zone: 'เหนือ',
        address: '456 ถนนทดสอบ 2',
        addressTh: '456 ถนนทดสอบ 2',
        addressEn: '456 Test Road 2',
        addressDesc: 'ใกล้ห้างสรรพสินค้า',
        branchPhone: '************',
        lineId: '@testbranch2',
        facebookId: 'testbranch2',
        facebookUrl: 'https://facebook.com/testbranch2',
        telegram_id: '@testbranch2',
        whatsapp_id: '66812345679',
        mapUrl: 'https://maps.google.com/test2',
        latitude: '18.7883',
        longitude: '98.9853',
      ),
      Branch(
        branchName: 'สาขาทดสอบ 3',
        branchNameTh: 'สาขาทดสอบ 3',
        branchNameEn: 'Test Branch 3',
        branchId: 'TEST003',
        province: 'ขอนแก่น',
        zone: 'อีสาน',
        address: '789 ถนนทดสอบ 3',
        addressTh: '789 ถนนทดสอบ 3',
        addressEn: '789 Test Road 3',
        addressDesc: 'ใกล้มหาวิทยาลัย',
        branchPhone: '************',
        lineId: '@testbranch3',
        facebookId: 'testbranch3',
        facebookUrl: 'https://facebook.com/testbranch3',
        telegram_id: '@testbranch3',
        whatsapp_id: '66812345680',
        mapUrl: 'https://maps.google.com/test3',
        latitude: '16.4322',
        longitude: '102.8236',
      ),
    ];
  }

  /// สร้าง mock API response สำหรับ getBranchData
  static Map<String, dynamic> createMockGetBranchResponse() {
    return {
      'status': 200,
      'result': createMockBranchData().map((branch) => branch.toJson()).toList(),
    };
  }

  /// สร้าง mock API response สำหรับ searchBranchData
  static Map<String, dynamic> createMockSearchBranchResponse(String keyword) {
    final allBranches = createMockBranchData();
    final filteredBranches = allBranches
        .where((branch) =>
            branch.branchName.toLowerCase().contains(keyword.toLowerCase()) ||
            branch.province.toLowerCase().contains(keyword.toLowerCase()))
        .toList();

    return {
      'status': 200,
      'result': filteredBranches.map((branch) => branch.toJson()).toList(),
    };
  }

  /// สร้าง mock error response
  static Map<String, dynamic> createMockErrorResponse() {
    return {
      'status': 500,
      'message': 'Internal Server Error',
    };
  }

  /// รอให้ animation เสร็จสิ้น
  static Future<void> waitForAnimations(WidgetTester tester) async {
    await tester.pumpAndSettle();
  }

  /// รอเวลาที่กำหนด
  static Future<void> waitFor(Duration duration) async {
    await Future.delayed(duration);
  }

  /// ตรวจสอบว่า widget มีอยู่และสามารถมองเห็นได้
  static void expectWidgetToBeVisible(Finder finder) {
    expect(finder, findsOneWidget);
  }

  /// ตรวจสอบว่า widget ไม่มีอยู่
  static void expectWidgetNotToExist(Finder finder) {
    expect(finder, findsNothing);
  }

  /// ตรวจสอบว่า text มีอยู่ในหน้าจอ
  static void expectTextToBeVisible(String text) {
    expect(find.text(text), findsOneWidget);
  }

  /// Tap widget และรอ animation
  static Future<void> tapAndWait(WidgetTester tester, Finder finder) async {
    await tester.tap(finder);
    await waitForAnimations(tester);
  }

  /// Enter text และรอ animation
  static Future<void> enterTextAndWait(
      WidgetTester tester, Finder finder, String text) async {
    await tester.enterText(finder, text);
    await waitForAnimations(tester);
  }

  /// ล้างข้อมูล GetX controllers
  static void cleanupGetX() {
    Get.reset();
  }

  /// Setup GetX bindings สำหรับการทดสอบ
  static void setupGetXForTesting() {
    Get.testMode = true;
  }

  /// สร้าง mock contract data สำหรับการทดสอบ LoanMainScreen
  static List<dynamic> createMockContractData() {
    return [
      {
        'ctt_code': 'AAM001DT001',
        'guarantee_type': '1',
        'guarantee_type_name': 'รถยนต์',
        'loan_amount': '500000',
        'interest': '5.5',
        'remain': '300000',
        'loan_periods': '36',
        'due_date': '2024-01-15',
        'period_amount': '15000',
        'period_status': 'ACTIVE'
      },
      {
        'ctt_code': 'AAM002DT002',
        'guarantee_type': '2',
        'guarantee_type_name': 'รถจักรยานยนต์',
        'loan_amount': '200000',
        'interest': '6.0',
        'remain': '150000',
        'loan_periods': '24',
        'due_date': '2024-01-20',
        'period_amount': '9000',
        'period_status': 'ACTIVE'
      }
    ];
  }

  /// สร้าง mock contract status data สำหรับการทดสอบ
  static List<dynamic> createMockContractStatusData() {
    return [
      {
        'guarantee_type': '1',
        'loan_amount': '300000',
        'loan_periods': '24',
        'loan_status': 'อยู่ระหว่างพิจารณาสินเชื่อ',
        'request_date': '2024-01-01'
      },
      {
        'guarantee_type': '3',
        'loan_amount': '800000',
        'loan_periods': '48',
        'loan_status': 'ผ่านการพิจารณาเรียบร้อย',
        'request_date': '2024-01-05'
      }
    ];
  }

  /// สร้าง mock API response สำหรับ getContractList
  static Map<String, dynamic> createMockGetContractListResponse() {
    return {
      'status': 200,
      'result': createMockContractData(),
    };
  }

  /// สร้าง mock API response สำหรับ getContractStatus
  static Map<String, dynamic> createMockGetContractStatusResponse() {
    return {
      'status': 200,
      'result': createMockContractStatusData(),
    };
  }

  /// สร้าง mock empty response
  static Map<String, dynamic> createMockEmptyResponse() {
    return {
      'status': 200,
      'result': [],
    };
  }
}
