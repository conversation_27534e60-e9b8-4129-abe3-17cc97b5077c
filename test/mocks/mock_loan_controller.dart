import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:AAMG/controller/request_loan/loan.controller.dart';

/// Mock LoanController สำหรับการทดสอบ LoanMainScreen
class MockLoanController extends GetxController {
  Rx<TextEditingController> loan_amount = TextEditingController().obs;
  FocusNode loanAmountFocus = FocusNode();

  RxString selectedGuarantee = ''.obs;
  RxInt selectedGuaranteeIndex = 0.obs;
  RxString selectedPeriod = ''.obs;
  RxInt selectedPeriodIndex = 0.obs;

  RxList<String> guaranteeList = [
    'รถยนต์',
    'รถจักรยานยนต์',
    'ที่ดิน',
    'AAM PAY'
  ].obs;

  RxList<String> guaranteeDescList = [
    'สินเชื่อรถยนต์',
    'สินเชื่อรถจักรยานยนต์', 
    'สินเชื่อที่ดิน',
    'AAM PAY'
  ].obs;

  RxList<String> periodList = [
    '6', '12', '18', '24', '36', '48', '60', '72', '84'
  ].obs;

  RxString? loan_status = '1'.obs;
  RxBool? isAcceptedTermPolicy = false.obs;

  // Mock flags สำหรับการทดสอบ
  bool shouldFailRequestLoan = false;
  bool _wasResetCalled = false;

  void calculateLoan(int loanAmount, String calculateType) {
    int total_loan = int.parse(loan_amount.value.text.isNotEmpty
        ? loan_amount.value.text.replaceAll(",", "")
        : '0');
    if (calculateType == 'plus') {
      total_loan += loanAmount;
      loan_amount.value.text = total_loan.toString();
    } else {
      if (loan_amount.value.text.isNotEmpty) {
        loan_amount.value.text = loan_amount.value.text
            .substring(0, loan_amount.value.text.length - 1);
      }
    }
    if (loan_amount.value.text.isNotEmpty) {
      final number =
          int.tryParse(loan_amount.value.text.replaceAll(',', '')) ?? 0;
      loan_amount.value.text = number.toString().replaceAllMapped(
            RegExp(r'(\d)(?=(\d{3})+(?!\d))'),
            (Match m) => '${m[1]},',
          );
    }
    update();
  }

  void selectGuarantee(String guarantee, int index) {
    if (selectedGuarantee.value != guarantee) {
      selectedGuarantee.value = guarantee;
      selectedGuaranteeIndex.value = index;
      selectedPeriod.value = '';
      selectedPeriodIndex.value = 0;
    } else {
      selectedGuarantee.value = guarantee;
      selectedGuaranteeIndex.value = index;
    }
    update();
  }

  void selectPeriod(String period, int index) {
    selectedPeriod.value = period;
    selectedPeriodIndex.value = index;
    update();
  }

  void setLoanAmount(String amount) {
    String amountStr = amount.replaceAll(',', '');
    final number = int.tryParse(amountStr) ?? 0;
    String formatted = number.toString().replaceAllMapped(
          RegExp(r'(\d)(?=(\d{3})+(?!\d))'),
          (Match m) => '${m[1]},',
        );
    if (loan_amount.value.text != formatted) {
      int selectionIndex = formatted.length -
          (loan_amount.value.text.length -
              loan_amount.value.selection.baseOffset);
      if (selectionIndex < 0) selectionIndex = 0;
      loan_amount.value.value = TextEditingValue(
        text: formatted,
        selection: TextSelection.collapsed(offset: selectionIndex),
      );
    }
  }

  Future<dynamic> requestLoan(BuildContext context) async {
    if (shouldFailRequestLoan) {
      throw Exception('Mock Request Loan Error');
    }

    // Mock successful loan request
    await Future.delayed(const Duration(milliseconds: 100));
    
    selectedGuarantee.value = '';
    loan_amount.value.text = '';
    selectedPeriod.value = '';
    isAcceptedTermPolicy!.value = false;
    update();
    
    return true;
  }

  Future<void> AcceptLoanAgreement() async {
    await resetAcceptTermPolicy();
    // Mock navigation to LoanRequestScreen
  }

  void acceptTermPolicy() {
    isAcceptedTermPolicy!.value = !isAcceptedTermPolicy!.value;
    update();
  }

  Future<void> resetAcceptTermPolicy() async {
    _wasResetCalled = true;
    isAcceptedTermPolicy!.value = false;
    update();
  }

  // Helper methods สำหรับการทดสอบ
  void setMockLoanData({
    String? amount,
    String? guarantee,
    int? guaranteeIndex,
    String? period,
    int? periodIndex,
    bool? acceptedPolicy,
  }) {
    if (amount != null) loan_amount.value.text = amount;
    if (guarantee != null) selectedGuarantee.value = guarantee;
    if (guaranteeIndex != null) selectedGuaranteeIndex.value = guaranteeIndex;
    if (period != null) selectedPeriod.value = period;
    if (periodIndex != null) selectedPeriodIndex.value = periodIndex;
    if (acceptedPolicy != null) isAcceptedTermPolicy!.value = acceptedPolicy;
    update();
  }

  void simulateRequestLoanError() {
    shouldFailRequestLoan = true;
  }

  void resetRequestLoanError() {
    shouldFailRequestLoan = false;
  }

  void resetMockData() {
    loan_amount.value.text = '';
    selectedGuarantee.value = '';
    selectedGuaranteeIndex.value = 0;
    selectedPeriod.value = '';
    selectedPeriodIndex.value = 0;
    loan_status!.value = '1';
    isAcceptedTermPolicy!.value = false;
    shouldFailRequestLoan = false;
    _wasResetCalled = false;
    update();
  }

  // Getters สำหรับการตรวจสอบใน tests
  bool get wasResetCalled => _wasResetCalled;
  bool get hasLoanAmount => loan_amount.value.text.isNotEmpty;
  bool get hasSelectedGuarantee => selectedGuarantee.value.isNotEmpty;
  bool get hasSelectedPeriod => selectedPeriod.value.isNotEmpty;
}
