import 'package:get/get.dart';
import 'package:AAMG/models/profile/profile_model.dart';
import 'package:AAMG/controller/profile/profile.controller.dart';

/// Mock ProfileController สำหรับการทดสอบ LoanMainScreen
class MockProfileController extends GetxController {
  Rx<Profile> profile = Profile().obs;

  // Mock flags สำหรับการทดสอบ
  bool _hasCompleteAddress = true;
  String? _mockAmphur;
  String? _mockProvince;

  @override
  void onInit() {
    super.onInit();
    _initializeMockProfile();
  }

  void _initializeMockProfile() {
    profile.value = Profile(
      firstname: '<PERSON>',
      lastname: '<PERSON><PERSON>',
      phone: '0812345678',
      phoneFirebase: '0812345678',
      idcard: '1234567890123',
      amphur: _hasCompleteAddress ? 'เมืองกรุงเทพ' : null,
      province: _hasCompleteAddress ? 'กรุงเทพมหานคร' : null,
      email: '<EMAIL>',
      birthdate: '1990-01-01',
      gender: 'M',
      address: '123 ถนนทดสอบ',
      zipcode: '10100',
    );
    update();
  }

  // Helper methods สำหรับการทดสอบ
  void setMockCompleteAddress(bool hasAddress) {
    _hasCompleteAddress = hasAddress;
    if (hasAddress) {
      _mockAmphur = 'เมืองกรุงเทพ';
      _mockProvince = 'กรุงเทพมหานคร';
    } else {
      _mockAmphur = null;
      _mockProvince = null;
    }
    
    profile.value = Profile(
      firstname: profile.value.firstname,
      lastname: profile.value.lastname,
      phone: profile.value.phone,
      phoneFirebase: profile.value.phoneFirebase,
      idcard: profile.value.idcard,
      amphur: _mockAmphur,
      province: _mockProvince,
      email: profile.value.email,
      birthdate: profile.value.birthdate,
      gender: profile.value.gender,
      address: profile.value.address,
      zipcode: profile.value.zipcode,
    );
    update();
  }

  void setMockProfile({
    String? firstname,
    String? lastname,
    String? phone,
    String? phoneFirebase,
    String? idcard,
    String? amphur,
    String? province,
    String? email,
    String? birthdate,
    String? gender,
    String? address,
    String? zipcode,
  }) {
    profile.value = Profile(
      firstname: firstname ?? profile.value.firstname,
      lastname: lastname ?? profile.value.lastname,
      phone: phone ?? profile.value.phone,
      phoneFirebase: phoneFirebase ?? profile.value.phoneFirebase,
      idcard: idcard ?? profile.value.idcard,
      amphur: amphur ?? profile.value.amphur,
      province: province ?? profile.value.province,
      email: email ?? profile.value.email,
      birthdate: birthdate ?? profile.value.birthdate,
      gender: gender ?? profile.value.gender,
      address: address ?? profile.value.address,
      zipcode: zipcode ?? profile.value.zipcode,
    );
    update();
  }

  void resetMockProfile() {
    _hasCompleteAddress = true;
    _mockAmphur = 'เมืองกรุงเทพ';
    _mockProvince = 'กรุงเทพมหานคร';
    _initializeMockProfile();
  }

  // Getters สำหรับการตรวจสอบใน tests
  bool get hasCompleteAddress => 
      profile.value.amphur != null && profile.value.province != null;
  
  String? get getMockAmphur => _mockAmphur;
  String? get getMockProvince => _mockProvince;
  bool get getMockHasCompleteAddress => _hasCompleteAddress;
}
