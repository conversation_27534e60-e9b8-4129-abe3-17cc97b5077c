import '../helpers/test_helpers.dart';

/// Mock HttpService สำหรับการทดสอบ
class MockHttpService {
  static bool shouldReturnError = false;
  static Map<String, dynamic>? customResponse;
  static List<Map<String, dynamic>> callHistory = [];

  /// Mock callAPIjwt method
  static Future<Map<String, dynamic>> callAPIjwt(
    String method,
    String endpoint,
    Map<String, dynamic> data,
  ) async {
    // บันทึกประวัติการเรียก API
    callHistory.add({
      'method': method,
      'endpoint': endpoint,
      'data': data,
      'timestamp': DateTime.now().toIso8601String(),
    });

    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 100));

    // Return custom response if set
    if (customResponse != null) {
      final response = Map<String, dynamic>.from(customResponse!);
      customResponse = null; // Reset after use
      return response;
    }

    // Return error if flag is set
    if (shouldReturnError) {
      return TestHelpers.createMockErrorResponse();
    }

    // Return appropriate mock response based on endpoint
    if (endpoint.contains('getBranchData')) {
      return TestHelpers.createMockGetBranchResponse();
    } else if (endpoint.contains('searchBranchData')) {
      final keyword = data['keyword'] as String? ?? '';
      return TestHelpers.createMockSearchBranchResponse(keyword);
    }

    // Default success response
    return {
      'status': 200,
      'result': [],
    };
  }

  /// Helper methods สำหรับการทดสอบ
  static void setCustomResponse(Map<String, dynamic> response) {
    customResponse = response;
  }

  static void simulateError() {
    shouldReturnError = true;
  }

  static void resetError() {
    shouldReturnError = false;
  }

  static void clearCallHistory() {
    callHistory.clear();
  }

  static List<Map<String, dynamic>> getCallHistory() {
    return List.from(callHistory);
  }

  static Map<String, dynamic>? getLastCall() {
    return callHistory.isNotEmpty ? callHistory.last : null;
  }

  static int getCallCount() {
    return callHistory.length;
  }

  static bool wasEndpointCalled(String endpoint) {
    return callHistory.any((call) => call['endpoint'].toString().contains(endpoint));
  }

  static Map<String, dynamic>? getCallForEndpoint(String endpoint) {
    try {
      return callHistory.firstWhere(
        (call) => call['endpoint'].toString().contains(endpoint),
      );
    } catch (e) {
      return null;
    }
  }

  static void reset() {
    shouldReturnError = false;
    customResponse = null;
    callHistory.clear();
  }
}
