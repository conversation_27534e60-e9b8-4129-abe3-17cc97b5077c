import 'package:get/get.dart';
import 'package:AAMG/controller/home/<USER>';

/// Mock HomeController สำหรับการทดสอบ LoanMainScreen
class MockHomeController extends GetxController {
  RxBool? isShowAAMNote = true.obs;
  var indexMenu = 0.obs;
  RxInt? indexMyloan = 0.obs;
  RxInt? indexNews = 0.obs;
  RxBool? showLoanData = false.obs;
  RxBool? isGuest = false.obs;
  RxString? showUpdateData = 'no'.obs;
  RxBool? isShowTutorial = false.obs;
  RxInt isIndexTutorial = 0.obs;
  RxInt? isIndexHome = 0.obs;
  RxBool closeHead = false.obs;

  // Mock flags สำหรับการทดสอบ
  bool _mockIsGuest = false;
  bool _mockShowLoanData = false;

  void closeAAMNote() {
    isShowAAMNote!.value = false;
    update();
  }

  void setIndexMenu(int index) {
    indexMenu.value = index;
    if (index == 0) {
      // Mock behavior for index 0
    } else if (index == 1) {
      indexMyloan!.value = 0;
    }
    update();
  }

  void setIndexMyloan(int index) {
    indexMyloan!.value = index;
    update();
  }

  void setIndexNews(int index) {
    indexNews!.value = index;
    update();
  }

  void setIndexTutorial(int index) {
    isIndexTutorial.value = index;
    update();
  }

  Future<void> setGuestUser() async {
    isGuest!.value = true;
    isShowTutorial!.value = true;
    _mockIsGuest = true;
    update();
  }

  void checkGuestData() {
    isGuest!.value = _mockIsGuest;
    update();
  }

  void changeCloseHead(input) {
    closeHead.value = input;
    update();
  }

  // Helper methods สำหรับการทดสอบ
  void setMockGuestStatus(bool isGuest) {
    _mockIsGuest = isGuest;
    this.isGuest!.value = isGuest;
    update();
  }

  void setMockShowLoanData(bool showData) {
    _mockShowLoanData = showData;
    showLoanData!.value = showData;
    update();
  }

  void resetMockData() {
    _mockIsGuest = false;
    _mockShowLoanData = false;
    isGuest!.value = false;
    showLoanData!.value = false;
    isShowAAMNote!.value = true;
    indexMenu.value = 0;
    indexMyloan!.value = 0;
    indexNews!.value = 0;
    showUpdateData!.value = 'no';
    isShowTutorial!.value = false;
    isIndexTutorial.value = 0;
    isIndexHome!.value = 0;
    closeHead.value = false;
    update();
  }

  // Getters สำหรับการตรวจสอบใน tests
  bool get getMockIsGuest => _mockIsGuest;
  bool get getMockShowLoanData => _mockShowLoanData;
}
