import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:AAMG/models/branch/branch_model.dart';
import 'package:AAMG/controller/branch/branch.controller.dart';
import '../helpers/test_helpers.dart';

/// Mock BranchController สำหรับการทดสอบ
class MockBranchController extends GetxController {
  RxList<Branch>? branchDataList = <Branch>[].obs;
  RxList<Branch>? branchInitList = <Branch>[].obs;
  RxString selectedZone = ''.obs;
  RxBool isActiveSelectZone = false.obs;
  RxBool isActiveSearchZone = false.obs;
  RxInt itemsToShow = 10.obs;
  Rx<TextEditingController> searchController = TextEditingController().obs;
  BitmapDescriptor? customIcon;
  FocusNode searchFocusNode = FocusNode();

  // Mock flags สำหรับการทดสอบ
  bool shouldFailGetBranchData = false;
  bool shouldFailSearchBranchData = false;
  bool isLoading = false;
  String? lastSearchKeyword;
  String? lastSelectedZone;

  @override
  void onInit() {
    super.onInit();
    // ไม่เรียก getBranchData() อัตโนมัติในการทดสอบ
  }

  /// Mock getBranchData method
  Future<dynamic> getBranchData() async {
    if (shouldFailGetBranchData) {
      throw Exception('Mock API Error');
    }

    isLoading = true;
    update();

    // Simulate API delay
    await Future.delayed(const Duration(milliseconds: 100));

    branchDataList!.clear();
    branchInitList!.clear();

    final mockData = TestHelpers.createMockBranchData();
    branchDataList!.value = mockData;
    branchInitList!.value = mockData;

    isLoading = false;
    update();
  }

  /// Mock setIntialBranchData method
  Future<dynamic> setIntialBranchData() async {
    branchDataList!.clear();
    if (branchInitList!.isEmpty) {
      await getBranchData();
    } else {
      branchDataList!.value = branchInitList!;
      update();
    }
  }

  /// Mock searchBranchData method
  Future<dynamic> searchBranchData(String keyword) async {
    lastSearchKeyword = keyword;

    if (shouldFailSearchBranchData) {
      setIntialBranchData();
      throw Exception('Mock Search API Error');
    }

    isLoading = true;
    branchDataList!.clear();
    update();

    // Simulate API delay
    await Future.delayed(const Duration(milliseconds: 100));

    final allBranches = TestHelpers.createMockBranchData();
    final filteredBranches = allBranches
        .where((branch) =>
            branch.branchName.toLowerCase().contains(keyword.toLowerCase()) ||
            branch.province.toLowerCase().contains(keyword.toLowerCase()) ||
            branch.zone.toLowerCase().contains(keyword.toLowerCase()))
        .toList();

    branchDataList!.value = filteredBranches;
    isLoading = false;
    update();
  }

  /// Mock sortBranchDataByZone method
  Future<dynamic> sortBranchDataByZone() async {
    if (branchInitList!.isEmpty) {
      return;
    }

    branchDataList!.value = branchInitList!
        .where((element) => element.zone == selectedZone.value)
        .toList();

    update();
  }

  /// Mock setActivateSelectZone method
  void setActivateSelectZone(value) {
    isActiveSelectZone.value = value;
    update();
  }

  /// Mock setActivateSearchZone method
  void setActivateSearchZone(value) {
    isActiveSearchZone.value = value;
    update();
  }

  /// Mock setSelectedZone method
  void setSelectedZone(String zone) {
    lastSelectedZone = zone;
    selectedZone.value = zone;
    update();
    sortBranchDataByZone();
  }

  /// Mock clearConfigData method
  void clearConfigData() {
    selectedZone.value = '';
    isActiveSelectZone.value = false;
    isActiveSearchZone.value = false;
    itemsToShow.value = 10;
    searchController.value.clear();
    lastSearchKeyword = null;
    lastSelectedZone = null;
    update();
  }

  // Helper methods สำหรับการทดสอบ
  void setMockData(List<Branch> data) {
    branchDataList!.value = data;
    branchInitList!.value = data;
    update();
  }

  void simulateApiError() {
    shouldFailGetBranchData = true;
    shouldFailSearchBranchData = true;
  }

  void resetApiError() {
    shouldFailGetBranchData = false;
    shouldFailSearchBranchData = false;
  }

  bool get hasData => branchDataList!.isNotEmpty;
  int get dataCount => branchDataList!.length;
  
  // Getters สำหรับการตรวจสอบใน tests
  String? get getLastSearchKeyword => lastSearchKeyword;
  String? get getLastSelectedZone => lastSelectedZone;
  bool get getIsLoading => isLoading;
}
