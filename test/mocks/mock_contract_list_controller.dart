import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:AAMG/models/contract/contract_list_model.dart';
import 'package:AAMG/models/contract/contract_pending_model.dart';
import 'package:AAMG/models/contract/period_model.dart';
import 'package:AAMG/controller/contract/contractlist.controller.dart';
import '../helpers/test_helpers.dart';

/// Mock ContractListController สำหรับการทดสอบ LoanMainScreen
class MockContractListController extends GetxController {
  RxString? custCode = RxString('');
  RxString? selectedLoan = RxString('');
  RxBool? isLoading_AAMPay = false.obs;
  RxBool? isLoading = false.obs;
  RxBool? chk_aampay = false.obs;
  RxBool? chkCountAAMPay = false.obs;
  RxBool? chk_contract = false.obs;
  RxList? contactPercent = [].obs;

  RxList<ContractList> contractList = <ContractList>[].obs;
  RxList<Period> contractPeriod = <Period>[].obs;
  RxList<ContractPending> contractStatus = <ContractPending>[].obs;

  RxList<String> guaranteeList = [
    'รถยนต์',
    'รถจักรยานยนต์',
    'ที่ดิน',
    'AAM PAY'
  ].obs;

  RxList<String> guaranteeDescList = [
    'สินเชื่อรถยนต์',
    'สินเชื่อรถจักรยานยนต์',
    'สินเชื่อที่ดิน',
    'AAM PAY'
  ].obs;

  RxList? guaranteeImgList = [].obs;
  RxList? guaranteeImgPercentList = [].obs;
  RxList? loanStatusStepList = [].obs;
  RxInt? selectedStatusContractIndex = 0.obs;
  RxBool isNewRequest = false.obs;
  RxInt? indexMyBill = 0.obs;

  // Mock flags สำหรับการทดสอบ
  bool shouldFailCustomerContactList = false;
  bool shouldFailGetContactStatus = false;
  String? lastSelectedContractIndex;

  @override
  void onInit() {
    super.onInit();
    setGuaranteeImg();
    // ไม่เรียก customerContactList() อัตโนมัติในการทดสอบ
  }

  void setGuaranteeImg() {
    guaranteeImgList = [
      '<svg>car_icon</svg>',
      '<svg>moto_icon</svg>',
      '<svg>land_icon</svg>',
      '<svg>aampay_icon</svg>'
    ].obs;

    guaranteeImgPercentList = [
      '<svg>car_percent_icon</svg>',
      '<svg>moto_percent_icon</svg>',
      '<svg>land_percent_icon</svg>',
      '<svg>aampay_percent_icon</svg>'
    ].obs;
    update();
  }

  Future<void> resetContractList() async {
    contractList.clear();
    contractPeriod.clear();
    contactPercent!.clear();
    update();
  }

  Future<dynamic> customerContactList() async {
    if (shouldFailCustomerContactList) {
      throw Exception('Mock API Error');
    }

    await resetContractList();

    // สร้าง mock contract data
    final mockContracts = TestHelpers.createMockContractData();
    for (var contract in mockContracts) {
      contractList.add(contract);
      
      // สร้าง mock period data
      final period = Period(
        period_no: '1',
        due_date: '2024-01-15',
        period_amount: '5000',
        period_interest: '500',
        period_principal: '4500',
        period_status: 'PAID',
      );
      contractPeriod.add(period);
      
      // เพิ่ม contact percent
      contactPercent!.add(0.5); // 50%
    }

    chk_contract!.value = contractList.isNotEmpty;
    update();
  }

  Future<dynamic> getContactStatus() async {
    if (shouldFailGetContactStatus) {
      throw Exception('Mock API Error');
    }

    contractStatus.clear();
    loanStatusStepList!.clear();

    // สร้าง mock contract status data
    final mockStatuses = TestHelpers.createMockContractStatusData();
    for (var status in mockStatuses) {
      contractStatus.add(status);
      loanStatusStepList!.add(2); // Mock status step
    }

    update();
    return contractStatus.isNotEmpty;
  }

  Future<void> selectedStatusContract(int index) async {
    lastSelectedContractIndex = index.toString();
    selectedStatusContractIndex!.value = index;
    update();
  }

  void setIndexMyBill(int index) {
    indexMyBill!.value = index;
    update();
  }

  String formatCurrency(String input) {
    try {
      int value = int.parse(input.replaceAll(',', ''));
      return value.toString().replaceAllMapped(
        RegExp(r'(\d)(?=(\d{3})+(?!\d))'),
        (Match m) => '${m[1]},',
      );
    } catch (e) {
      return input;
    }
  }

  String getGuaranteeTypeName(int index) {
    if (index >= contractList.length) return '';
    
    final contract = contractList[index];
    switch (contract.guarantee_type.toString()) {
      case "1":
        return guaranteeList[0];
      case "2":
        return guaranteeList[1];
      case "3":
        return guaranteeList[2];
      default:
        return guaranteeList[3];
    }
  }

  String setDescByGuaType(String guarantee_type) {
    switch (guarantee_type) {
      case '1':
        return guaranteeDescList[0].toString();
      case '2':
        return guaranteeDescList[1].toString();
      case '3':
        return guaranteeDescList[2].toString();
      default:
        return guaranteeDescList[3].toString();
    }
  }

  // Helper methods สำหรับการทดสอบ
  void setMockContractData(List<ContractList> contracts) {
    contractList.value = contracts;
    update();
  }

  void setMockContractStatusData(List<ContractPending> statuses) {
    contractStatus.value = statuses;
    update();
  }

  void simulateApiError() {
    shouldFailCustomerContactList = true;
    shouldFailGetContactStatus = true;
  }

  void resetApiError() {
    shouldFailCustomerContactList = false;
    shouldFailGetContactStatus = false;
  }

  // Getters สำหรับการตรวจสอบใน tests
  bool get hasContracts => contractList.isNotEmpty;
  bool get hasContractStatus => contractStatus.isNotEmpty;
  int get contractCount => contractList.length;
  int get contractStatusCount => contractStatus.length;
  String? get getLastSelectedContractIndex => lastSelectedContractIndex;
}
