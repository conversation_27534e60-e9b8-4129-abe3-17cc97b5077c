#!/bin/bash

# Test Runner Script สำหรับ AAMG Project
# สคริปต์นี้ใช้สำหรับรันการทดสอบทั้งหมดและสร้างรายงาน

echo "🚀 Starting AAMG Test Suite..."
echo "=================================="

# สร้างโฟลเดอร์สำหรับรายงานถ้ายังไม่มี
mkdir -p test_reports/coverage/html
mkdir -p test_reports/unit_tests
mkdir -p test_reports/widget_tests
mkdir -p test_reports/integration_tests

# ตั้งค่าสีสำหรับ output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function สำหรับแสดงผล
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# ตรวจสอบว่า Flutter พร้อมใช้งาน
print_status "Checking Flutter installation..."
if ! command -v flutter &> /dev/null; then
    print_error "Flutter is not installed or not in PATH"
    exit 1
fi

print_success "Flutter is ready"

# ดาวน์โหลด dependencies
print_status "Getting Flutter dependencies..."
flutter pub get

if [ $? -eq 0 ]; then
    print_success "Dependencies downloaded successfully"
else
    print_error "Failed to download dependencies"
    exit 1
fi

# รัน Unit Tests
print_status "Running Unit Tests..."
flutter test test/unit_test/ --reporter=json > test_reports/unit_tests/test_results.json

if [ $? -eq 0 ]; then
    print_success "Unit tests completed"
else
    print_warning "Some unit tests failed"
fi

# รัน Widget Tests
print_status "Running Widget Tests..."
flutter test test/widget_test/ --reporter=json > test_reports/widget_tests/test_results.json

if [ $? -eq 0 ]; then
    print_success "Widget tests completed"
else
    print_warning "Some widget tests failed"
fi

# รัน Integration Tests
print_status "Running Integration Tests..."
flutter test test/integration_test/ --reporter=json > test_reports/integration_tests/test_results.json

if [ $? -eq 0 ]; then
    print_success "Integration tests completed"
else
    print_warning "Some integration tests failed"
fi

# รัน All Tests พร้อม Coverage
print_status "Running all tests with coverage..."
flutter test --coverage

if [ $? -eq 0 ]; then
    print_success "All tests with coverage completed"
    
    # สร้าง HTML Coverage Report (ถ้ามี genhtml)
    if command -v genhtml &> /dev/null; then
        print_status "Generating HTML coverage report..."
        genhtml coverage/lcov.info -o test_reports/coverage/html/
        print_success "HTML coverage report generated at test_reports/coverage/html/index.html"
    else
        print_warning "genhtml not found. Install lcov to generate HTML coverage reports"
    fi
    
    # คัดลอก coverage data
    if [ -f "coverage/lcov.info" ]; then
        cp coverage/lcov.info test_reports/coverage/
        print_success "Coverage data copied to test_reports/coverage/"
    fi
else
    print_error "Tests with coverage failed"
fi

# สร้างสรุปรายงาน
print_status "Generating test summary..."

# นับจำนวน test files
UNIT_TEST_COUNT=$(find test/unit_test -name "*.dart" | wc -l)
WIDGET_TEST_COUNT=$(find test/widget_test -name "*.dart" | wc -l)
INTEGRATION_TEST_COUNT=$(find test/integration_test -name "*.dart" | wc -l)

# นับจำนวน test cases โดยประมาณ
BRANCH_SCREEN_TESTS=$(grep -c "testWidgets\|test(" test/unit_test/branch_screen_test.dart test/widget_test/branch_screen_widget_test.dart test/integration_test/branch_controller_test.dart 2>/dev/null || echo "0")
LOAN_MAIN_SCREEN_TESTS=$(grep -c "testWidgets\|test(" test/unit_test/loan_main_screen_test.dart test/widget_test/loan_main_screen_widget_test.dart test/integration_test/loan_main_screen_integration_test.dart 2>/dev/null || echo "0")

# สร้างไฟล์สรุป
cat > test_reports/test_summary.md << EOF
# Test Summary Report

**Generated on:** $(date)

## Test Statistics
- Unit Test Files: ${UNIT_TEST_COUNT}
- Widget Test Files: ${WIDGET_TEST_COUNT}
- Integration Test Files: ${INTEGRATION_TEST_COUNT}
- Total Test Files: $((UNIT_TEST_COUNT + WIDGET_TEST_COUNT + INTEGRATION_TEST_COUNT))

## Test Cases by Screen
- BranchScreen Tests: ${BRANCH_SCREEN_TESTS}
- LoanMainScreen Tests: ${LOAN_MAIN_SCREEN_TESTS}
- Total Test Cases: $((BRANCH_SCREEN_TESTS + LOAN_MAIN_SCREEN_TESTS))

## Test Categories

### Unit Tests
- BranchScreen functionality tests
- BranchController method tests
- LoanMainScreen functionality tests
- Mock data validation tests

### Widget Tests
- BranchScreen UI component tests
- LoanMainScreen UI component tests
- User interaction tests
- Responsive design tests

### Integration Tests
- BranchController integration tests
- LoanMainScreen controller integration tests
- Business logic workflow tests
- Error handling tests

## Coverage Information
- Coverage report available at: test_reports/coverage/html/index.html
- LCOV data available at: test_reports/coverage/lcov.info

## How to View Results
1. Open test_reports/coverage/html/index.html in a browser for coverage report
2. Check individual test result JSON files in respective folders
3. Review this summary for overall test statistics

## Next Steps
1. Review failed tests and fix issues
2. Add more test cases for better coverage
3. Update tests when adding new features
4. Maintain test quality and documentation
EOF

print_success "Test summary generated at test_reports/test_summary.md"

echo ""
echo "=================================="
print_success "Test suite completed!"
echo "📊 Check test_reports/ folder for detailed results"
echo "🌐 Open test_reports/coverage/html/index.html for coverage report"
echo "📋 View test_reports/test_summary.md for summary"
echo "=================================="
