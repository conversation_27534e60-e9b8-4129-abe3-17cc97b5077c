import 'package:AAMG/controller/AppConfigService.dart';
import 'package:AAMG/controller/news_promotions/news.controller.dart';
import 'package:AAMG/controller/transalation/translation_key.dart';
import 'package:AAMG/view/componance/themes/app_colors.dart';
import 'package:AAMG/view/componance/themes/theme.dart';
import 'package:AAMG/view/componance/utils/AppImageAssets.dart';
import 'package:AAMG/view/componance/utils/constant/size.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';

import '../../../models/news_model.dart';
import '../../componance/utils/AppSvgImage.dart';
import '../../componance/widgets/common_widgets/custom_bottom_sheet.dart';
import '../feedback/widgets/feedback_notification.dart';

class NewsDetail extends StatefulWidget {
  final int index;

  NewsDetail({required this.index});

  @override
  State<NewsDetail> createState() => _NewsDetailState();
}

class _NewsDetailState extends State<NewsDetail> {

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _showFeedbackNotiBottomSheet(context);
    });
  }

  void _showFeedbackNotiBottomSheet(BuildContext context) {
    Get.bottomSheet(
      const CustomBottomSheet(
          height: 90,
          borderRadius: 0.0,
          showBorderTop: true,
          padding: EdgeInsets.all(CSizes.defaultSpace),
          child : FeedbackNotiContent()
      ),
      barrierColor: Colors.transparent,
    );
  }

  @override
  Widget build(BuildContext context) {
    DateFormat format = new DateFormat("dd-MM-yyyy");
    AppConfigService appConfigService = Get.find<AppConfigService>();

    int index = this.widget.index;
    return Scaffold(
      body: GetBuilder<NewsController>(
        init: NewsController(),
        builder: (news) => Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              margin: EdgeInsets.only(top: 55.h,left: 14.w,right: 14.w),
              child: Row(
                children: [
                  GestureDetector(
                    onTap: () {
                      Get.back();
                    },
                    child: SizedBox(
                        height: 24.h,
                        width: 24.w,
                        child: SvgPicture.string(AppSvgImage.back_btn)
                    )
                  ),
                  Container(
                    height: 34.h,
                    width: 34.w,
                    decoration: BoxDecoration(
                      image: DecorationImage(
                        image: AssetImage(
                          appConfigService.countryConfigCollection.toString() == "aam"
                              ? AppImageAssets.aam_notify_aam
                              : appConfigService.countryConfigCollection.toString() == "rafco"
                              ? AppImageAssets.rafco_notify_rafco
                              : AppImageAssets.rplc_notify_rplc,
                        ),
                      ),
                    ),
                  ),
                  SizedBox(width: 8.w,),
                  Container(
                    child: Text(
                      notificationDetail.tr,
                      style: TextStyle(
                        color: configTheme().textTheme.bodyMedium?.color,
                        fontSize: configTheme().primaryTextTheme.bodyLarge?.fontSize,
                        fontFamily:
                        configTheme().primaryTextTheme.bodyLarge?.fontFamily,
                        fontWeight:
                        configTheme().primaryTextTheme.bodyLarge?.fontWeight,
                        // height: 0.09,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(height: 6.h,),
            Divider(
              indent: 0,
              endIndent: 0,
              color: Colors.black.withOpacity(0.15),
            ),
            Container(
              margin: EdgeInsets.only(top: 12.h, bottom: 16.h,left: 14.w,right: 14.w),
              height: 347.h,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                image: DecorationImage(
                  image: NetworkImage(
                      news.newsLastestList![index].urlImgNews.toString()),
                  fit: BoxFit.cover,
                ),
                // color: Colors.white,
              ),
            ),
            Padding(
              padding:  EdgeInsets.only(left: 14.w,right: 14.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    news.newsLastestList![index].dateNews.toString(),
                    style: TextStyle(
                      color: configTheme()
                          .textTheme
                          .bodyMedium
                          ?.color
                          ?.withOpacity(0.5),
                      fontSize: configTheme().textTheme.labelSmall?.fontSize,
                      // fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    news.newsLastestList![index].headerNews.toString(),
                    style: TextStyle(
                      color: configTheme().textTheme.bodyMedium?.color,
                      fontSize: configTheme().primaryTextTheme.titleLarge?.fontSize,
                      fontFamily:
                      configTheme().primaryTextTheme.titleLarge?.fontFamily,
                      fontWeight:
                      configTheme().primaryTextTheme.titleLarge?.fontWeight,
                      // height: 0.09,
                    ),
                  ),
                  Text(
                    news.newsLastestList![index].subHeaderNews.toString(),
                  ),
                  Text(
                      appConfigService.countryConfigCollection.toString() == 'aam' || appConfigService.countryConfigCollection.toString() == 'rplc'
                          ? news
                          .parseHtmlString(news.newsLastestList[index].bodyNews)
                          : news.newsLastestList[index].bodyNews.toString(),
                      style: TextStyle(
                        color: configTheme().textTheme.bodyMedium?.color ??
                            AppColors.textBlackColor.withOpacity(0.75),
                        // fontSize:20,
                        fontSize: configTheme().textTheme.bodySmall?.fontSize,
                        fontFamily: configTheme().textTheme.bodySmall?.fontFamily,
                        // fontWeight: FontWeight.bold,
                      )),

                ],
              ),
            )
            // GestureDetector(
            //   onTap: (){
            //     Get.back();
            //   },
            //   child: Container(
            //     // height: 40.h,width: 40.w,
            //     decoration: BoxDecoration(
            //       shape: BoxShape.circle,
            //       color: Colors.black.withOpacity(0.5)
            //     ),child: Center(
            //       child: Icon(Icons.arrow_back_ios,
            //         color: Colors.white,
            //         size: 20,
            //       )),
            //   ),
            // ),
          ],
        ),
      ),
    );
  }
}
