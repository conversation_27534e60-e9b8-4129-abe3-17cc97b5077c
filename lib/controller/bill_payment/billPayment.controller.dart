import 'dart:async';
import 'dart:io';
import 'dart:typed_data';

import 'package:AAMG/view/componance/themes/theme.dart';
import 'package:camera/camera.dart';
import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
// import 'package:image_gallery_saver/image_gallery_saver.dart';
import 'package:image_gallery_saver_plus/image_gallery_saver_plus.dart';
import 'package:intl/intl.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:sentry/sentry.dart';

import '../../service/AppService.dart';
import '../../service/endpoint.dart';
import '../../service/http_service.dart';
import '../../view/componance/AppLoading.dart';
import '../contract/contractlist.controller.dart';
import '../contract/myloan.controller.dart';
import '../profile/profile.controller.dart';
import '../service/AppService.dart';
import '../transalation/transalation.controller.dart';
import 'package:http/http.dart' as http;


class BillPaymentController extends GetxController {
  RxBool isSpectifyAmount = false.obs;
  FocusNode amountFocusNode = FocusNode();
  Rx<TextEditingController> amountController = TextEditingController().obs;
  Rx<TextEditingController> qrAmountController = TextEditingController().obs;
  RxInt selectedContractIndex = 0.obs;
  RxString transectionDate = "".obs;
  RxString transectionTime = "".obs;
  RxString bil_ctt_code = "".obs;
  RxBool isSaveQRImg = false.obs;
  //TODO กำหนดเวลานับถอยหลัง
  Duration _remaining = const Duration();
  RxString count_qrExpired = ''.obs;
  RxString? timeQRDataExpired = ''.obs;
  RxString? qrCode_Url = ''.obs;
  RxString? qrCode_data = ''.obs;
  RxString slip_url = ''.obs;
  RxBool isUploadSlipSuccess = false.obs;

  RxString dueDateLocalFormat = ''.obs; //TODO วันที่ครบกำหนดชำระตามภาษาท้องถิ่น

  int findContractIndexByCttCode(String cttCode) {
    final ContractListController contractListCtl =
    Get.find<ContractListController>();
    for (int i = 0; i < contractListCtl.contractList.length; i++) {
      if (contractListCtl.contractList[i].ctt_code == cttCode) {
        return i;
      }
    }
    return 0; // ถ้าไม่พบ ctt_code ใน contractList
  }

  Future<void> setInitailContract(int index, String ctt_code) async {
    try {
      final ContractListController contractListCtl =
          Get.find<ContractListController>();
      selectedContractIndex.value = index;
      amountController.value.text = AppService.formatCurrencyWithDecimal(
          contractListCtl.contractList[index].nextpay.toString());
      transectionDate.value = await contractListCtl.setDueDateLocale(
          DateTime.now().toString(),
          Get.find<TransalationController>().location.value == 'English'
              ? 'en'
              : Get.find<TransalationController>().location.value == 'Thailand' || appConfigService.countryConfigCollection == 'aam'
                  ? 'th'
                  : Get.find<TransalationController>().location.value ==
                              'Lao' ||
                          appConfigService.countryConfigCollection == 'rplc'
                      ? 'lo'
                      : Get.find<TransalationController>().location.value ==
                                  'Cambodian' ||
                              appConfigService.countryConfigCollection ==
                                  'rafco'
                          ? 'km'
                          : 'en');
      dueDateLocalFormat.value = await contractListCtl.setDueDateLocale(
          Get.find<ContractListController>()
              .contractList[index]
              .due_date
              .toString(),
          Get.find<TransalationController>().location.value == 'English'
              ? 'en'
              : Get.find<TransalationController>().location.value == 'Thailand' || appConfigService.countryConfigCollection == 'aam'
              ? 'th'
              : Get.find<TransalationController>().location.value ==
              'Lao' ||
              appConfigService.countryConfigCollection == 'rplc'
              ? 'lo'
              : Get.find<TransalationController>().location.value ==
              'Cambodian' ||
              appConfigService.countryConfigCollection ==
                  'rafco'
              ? 'km'
              : 'en');
      bil_ctt_code.value = ctt_code;
      update();
      print('amountController : ${amountController.value.text}');
      print('dueDateLocalFormat : ${dueDateLocalFormat.value}');
    } catch (e) {
      print(e);
    }
  }

  Future<dynamic> getQrCodePayment() async {
    try {
      final ProfileController profileCtl = Get.find<ProfileController>();
      final MyloanController myLoanCtl = Get.find<MyloanController>();

      // print('###');
      // print(profileCtl.profile.value.phone.obs.string);
      // print(myLoanCtl.myLoanList[0].from.obs.string);
      // print(myLoanCtl.myLoanList[0].nextpayQR.obs.string);
      // print(myLoanCtl.myLoanList[0].ctt_code.obs.string);
      // print(myLoanCtl.myLoanList[0].branchPICO!.obs.string);
      // print('###');
      // print('### : ${myLoanCtl.myLoanList.first}');

      Map data = {
        "from": myLoanCtl.myLoanList[0].from.obs.string,
        // "amount": myLoanCtl.myLoanList[0].nextpayQR.obs.string, // not allow to change amount
        "amount": amountController.value.text.replaceAll(',', ''),
        "ctt_code": myLoanCtl.myLoanList[0].ctt_code.obs.string,
        "phone": profileCtl.profile.value.phone.obs.string,
        "branchPICO": myLoanCtl.myLoanList[0].branchPICO!.obs.string,
      };

      // print('data : $data');

      AppLoading.loadingVerify(Get.context);

      final response =
          await HttpService.callAPIjwt('POST', Endpoints.getQRPayment, data);
      AppLoading.Loaderhide(Get.context);

      // print('response : $response');

      if (response['status'] == 200) {
        qrCode_data!.value = response['result'][0]['qrcode'];
        qrCode_Url!.value = response['result'][0]["qrcode_url"];
        // qrCode_Url!.value =
        //     'https://linebotkeep-file.s3.ap-southeast-1.amazonaws.com/template_up/1731057480.png';
        qrAmountController.value.text = amountController.value.text;
        update();
      } else {
        // getQrCodePayment();
        debugPrint('getQrCodePayment : ${response['message']}');
        // qrCode_Url!.value =
        //     'https://linebotkeep-file.s3.ap-southeast-1.amazonaws.com/template_up/1731057480.png';
        // update();
        _remaining = Duration(minutes: 0); //ยังไม่นับเวลา
        update();
      }
    } catch (exception) {
      // getQrCodePayment();
      _remaining = Duration(minutes: 0); //ยังไม่นับเวลา
      update();
      // await Sentry.captureException(
      //   exception,
      //   stackTrace: stackTrace,
      // );
      if (kDebugMode) {
        print('getQrCodePayment : $exception');
      }
      // const GetSnackBar(
      //   title: "เกิดข้อผิดพลาด",
      //   message: "เกิดข้อผิดพลาด",
      //   duration: Duration(seconds: 3),
      // );

    }
  }

  Future<void> saveQRCodeImage_() async {
    try {
      Map<Permission, PermissionStatus> statuses = await [
        Permission.storage,
      ].request();

      var imgQR_name = appConfigService.countryConfigCollection == 'aam'
          ? 'AAMQRCode_'
          : appConfigService.countryConfigCollection == 'rplc'
              ? 'RPLCQRCode_'
              : 'RAFCOQRCode_';

      if (await Permission.storage.request().isGranted) {
        // isLoader!.value = true;
        // update();
        var response = await Dio().get(qrCode_Url!.value,
            options: Options(responseType: ResponseType.bytes));

        final result = await ImageGallerySaverPlus.saveImage(
            Uint8List.fromList(response.data),
            quality: 60,
            name: "$imgQR_name${DateTime.now().millisecondsSinceEpoch}.png");

        if (result != null) {
          setStatusSaveImg(true);
          //TODO ปิด popup หลังจากบันทึก QR Code สำเร็จ
          Future.delayed(Duration(seconds: 3), () {
            setStatusSaveImg(false);
          });
        } else {
          setStatusSaveImg(false);
          Get.snackbar("แจ้งเตือน", "ไม่สามารถบันทึกได้");
        }
      } else {
        setStatusSaveImg(false);
        Get.snackbar("แจ้งเตือน", "ไม่สามารถบันทึกได้");
      }
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
      setStatusSaveImg(false);
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }
  }

  Future<void> saveQRCodeImage() async {
    try {
      // ขอ permission
      PermissionStatus status = await Permission.storage.request();

      if (status.isGranted) {
        // สร้างชื่อไฟล์ QR Code ตาม countryConfigCollection
        var imgQR_name = appConfigService.countryConfigCollection == 'aam'
            ? 'AAMQRCode_'
            : appConfigService.countryConfigCollection == 'rplc'
            ? 'RPLCQRCode_'
            : 'RAFCOQRCode_';

        var response = await Dio().get(
          qrCode_Url!.value,
          options: Options(responseType: ResponseType.bytes),
        );

        final result = await ImageGallerySaverPlus.saveImage(
          Uint8List.fromList(response.data),
          quality: 60,
          name: "$imgQR_name${DateTime.now().millisecondsSinceEpoch}.png",
        );

        if (result != null) {
          setStatusSaveImg(true);
          Future.delayed(Duration(seconds: 3), () {
            setStatusSaveImg(false);
          });
        } else {
          // setStatusSaveImg(false);
          // Get.snackbar("แจ้งเตือน", "ไม่สามารถบันทึกได้");
          // openAppSettings();
          saveImage(qrCode_Url!.value);

        }
      } else if (status.isPermanentlyDenied) {
        // ถ้าผู้ใช้ปฏิเสธแบบถาวร ให้พาไปหน้า Settings
        // openAppSettings();
        saveImage(qrCode_Url!.value);

      } else {
        // openAppSettings();
        // setStatusSaveImg(false);
        // Get.snackbar("แจ้งเตือน", "ไม่สามารถบันทึกได้");
        saveImage(qrCode_Url!.value);

      }
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
      setStatusSaveImg(false);
      Get.snackbar("เกิดข้อผิดพลาด", "เกิดข้อผิดพลาดขณะบันทึกภาพ");
    }
  }


  Future<void> saveImage(imageUrl) async {
    print('เข้าฟังก์ชัน imageUrl : $imageUrl');
    var response = await http.get(Uri.parse(imageUrl));
    final bytes = response.bodyBytes;
    final result = await ImageGallerySaverPlus.saveImage(bytes);

    if (result['isSuccess']) {
      debugPrint('บันทึกรูปภาพสำเร็จ');
      setStatusSaveImg(true);
      Future.delayed(Duration(seconds: 3), () {
        setStatusSaveImg(false);
      });
    } else {
      debugPrint('เกิดข้อผิดพลาดในการบันทึกรูปภาพ');
      Get.snackbar("แจ้งเตือน", "ไม่สามารถบันทึกได้");
      setStatusSaveImg(false);
      openAppSettings();
    }
  }


  Future<void> cameraImgPayment(context) async {
    try {
      AppLoading.loadingVerify(context);
      //TODO ถ่ายรูปสลิป
      File? img_file = await AppUploadService.cameraImage();
      //TODO อัพโหลดรูปขึ้น s3
      slip_url.value =
          await AppUploadService.upLoadImgToS3_V1(img_file, "bill");
      update();

      // if (slip_url.value != null) {
      //   // Get.back();
      //   throw Exception("เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง");
      // }
      //then

      uploadSlipPayment(context);
    } catch (e) {
      AppLoading.Loaderhide(context);
      Get.back();
      if (kDebugMode) {
        print(e);
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }
  }

  Future<void> chooseImgPayment(context) async {
    try {
      AppLoading.loadingVerify(context);
      //TODO เลือกรูปสลิป
      File? img_file = await AppUploadService.pickImage();
      //TODO อัพโหลดรูปขึ้น s3
      slip_url.value =
          await AppUploadService.upLoadImgToS3_V1(img_file, "bill");
      update();

      // if (slip_url.value != null) {
      //   Get.back();
      //   throw Exception("เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง");
      // }
      //then

      uploadSlipPayment(context);
    } catch (e) {
      AppLoading.Loaderhide(context);
      Get.back();
      if (kDebugMode) {
        print(e);
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }
  }

  Future<dynamic> uploadSlipPayment(context) async {
    try {
      print('slip_url : ${slip_url.value}');
      final now = DateTime.now();
      final MyloanController myLoanCtl = Get.find<MyloanController>();
      //TODO อัพโหลดสลิป
      Map data = {
        "pay_date": now.toString(),
        "ctt_code": myLoanCtl.myLoanList[0].ctt_code.obs.string,
        "cust_name": myLoanCtl.myLoanList[0].fullname.obs.string,
        "cust_tel":
            Get.find<ProfileController>().profile.value.phone.obs.string,
        "pt_name": "เงินโอน",
        "cpy_code": "",
        "bill_img": slip_url.value,
        "bill_type": "ใบเสร็จค่างวด",
        "send_from": appConfigService.countryConfigCollection == 'aam'
            ? "AppAAM"
            : appConfigService.countryConfigCollection == 'rplc'
                ? "AppRPLC"
                : "AppRAFCO",
        "branch": myLoanCtl.myLoanList[0].branchPICO!.obs.string,
      };

      final response = await HttpService.callAPIjwt(
          'POST', Endpoints.uploadSlipBillPayment, data);
      AppLoading.Loaderhide(context);
      if (response['status'] == 200) {
        isUploadSlipSuccess.value = true;
      } else {
        isUploadSlipSuccess.value = false;
      }
      update();
      Get.back();
    } catch (e) {
      isUploadSlipSuccess.value = false;
      update();
      Get.back();
      if (kDebugMode) {
        print(e);
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }
  }

  Future<void> setTimeQRDataExpired() async {

    try{


    print('qrAmountController : ${qrAmountController.value.text}');
    print('amountController : ${amountController.value.text}');
    if(qrAmountController.value.text != amountController.value.text) {
      print('เปลี่ยนจำนวนเงิน');
      qrCode_Url!.value = "";
      _remaining = Duration(minutes: 0); // ตั้งเวลา 0 นาที
      update();
      print("##########");
      print(_remaining.inSeconds);
      await getQrCodePayment();
      if (qrCode_Url!.value != null &&
          qrCode_Url!.value != "") {
        print('เริ่มนับเวลาใหม่');
        _remaining = Duration(minutes: 10); // ตั้งเวลา 10 นาที
        update();
        startTimer();
      }

    }else if (qrCode_Url!.value != null &&
        qrCode_Url!.value != "" &&
        qrCode_Url!.value.toString().trim().isNotEmpty) {
      if (_remaining.inSeconds == 0) {
        startTimer();
      }
    } else {
      print('ไม่มีข้อมูล QR Code');
      await getQrCodePayment();

      if (qrCode_Url!.value != null &&
          qrCode_Url!.value != "" &&
          _remaining.inSeconds == 0) {
        print('เริ่มนับเวลาใหม่');
        _remaining = Duration(minutes: 10); // ตั้งเวลา 10 นาที
        update();
        startTimer();
      }
    }
    }catch(e){
      print('setTimeQRDataExpired $e');
    }

  }

  Future<void> startTimer() async {
    if (_remaining.inSeconds == 0) {
      _remaining = Duration(minutes: 10); // ตั้งเวลา 10 นาที
      update();
    }
    Timer timer = Timer.periodic(Duration(seconds: 1), (timer) {
      _remaining -= Duration(seconds: 1);
      update(); // Update UI using GetX update()

      //TODO ปรับ format
      formatDuration(_remaining);

      // บวกเวลา 10 นาที สำหรับโชว์ หน้า UI
      // DateTime now = getTime();
      // DateTime tenMinutesLater = addMinutes(now, 10);
      // timeQRDataExpired!.value =
      //     AppService.extractDateAndTime(tenMinutesLater.toString());
      update();
      if (_remaining.inSeconds <= 0) {
        timer.cancel();
        print('เวลาหมด!');
      }
    });
  }

  String formatDuration(Duration duration) {
    // แปลงวินาทีเป็นนาที
    String minutes = duration.inMinutes.toString().padLeft(2, '0');

    // แปลงวินาทีที่เหลือเป็นวินาที
    int secondsRemaining = duration.inSeconds % 60;
    String seconds = secondsRemaining.toString().padLeft(2, '0');

    // แปลงวินาทีที่เหลือเป็นเศษส่วนของวินาที
    int milliseconds = duration.inMilliseconds % 1000;
    String millisecondsString = milliseconds.toString().padLeft(2, '0');
    count_qrExpired.value = '$minutes:$seconds:$millisecondsString';
    update();
    return '$minutes:$seconds:$millisecondsString';
  }

  DateTime getTime() {
    return DateTime.now(); // ดึงเวลาปัจจุบัน
  }

  DateTime addMinutes(DateTime dateTime, int minutes) {
    return dateTime.add(Duration(minutes: minutes)); // บวกเวลาเพิ่ม
  }

  void setSelectPayAmount(bool value) {
    isSpectifyAmount.value = value;
    update();
    AppService.hideKeyboard();
    if (value == false) {
      amountController.value.text = AppService.formatCurrencyWithDecimal(
          Get.find<ContractListController>()
              .contractList[selectedContractIndex.value]
              .nextpay
              .toString());
    }
  }

  void setAmount(value) {
    var amount = value.toString();
    debugPrint(
        'ยอดคงเหลือ : ${Get.find<ContractListController>().contractList[selectedContractIndex.value].remain.toString()}');
    //TODO ถ้าระบุจำนวนเงินมากกว่ายอดค้างชำระ ให้เปลี่ยนเป็นยอดค้างชำระ (remain)
    if (int.parse(value.toString().replaceAll(',', '')) >
        int.parse(Get.find<ContractListController>()
            .contractList[selectedContractIndex.value]
            .remain
            .toString()
            .replaceAll(',', ''))) {
      amount = Get.find<ContractListController>()
          .contractList[selectedContractIndex.value]
          .remain
          .toString()
          .replaceAll(',', '');
      update();
    }


    amountController.value.text =
        AppService.formatCurrencyWithDecimal(amount.toString());
    update();

    print("sdsdsds");
    print(amountController.value.text);
  }

  void setStatusSaveImg(bool value) {
    isSaveQRImg.value = value;
    update();
  }

  void getCurrentTimeTransection() {
    final now = DateTime.now();
    final timeFormat = DateFormat("HH:mm");
    transectionTime.value = timeFormat.format(now);
    update();
  }

  void resetData() {
    amountController.value.text = "";
    selectedContractIndex.value = 0;
    transectionDate.value = "";
    isSpectifyAmount.value = false;
    bil_ctt_code.value = "";
    isSaveQRImg.value = false;
    count_qrExpired.value = "";
    timeQRDataExpired!.value = "";
    qrCode_Url!.value = "";
    qrCode_data!.value = "";
    slip_url.value = "";
    isUploadSlipSuccess.value = false;
    update();
  }
}
