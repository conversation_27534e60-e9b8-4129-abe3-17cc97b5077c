import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:html/parser.dart';
import 'package:AAMG/service/http_service.dart';

import '../../models/news_model.dart';
import '../../service/endpoint.dart';

class NewsController extends GetxController {
  NewModelList newModelList = NewModelList();
  RxList<NewModel> newsLastestList = <NewModel>[].obs;
  RxList<NewModel> newsList = <NewModel>[].obs;

  @override
  onInit() {
    super.onInit();
    Future.delayed(Duration.zero, () {
      getNewsPromotion();
    });
  }

  Future<dynamic> getNewsPromotion() async {
    try {
      newsList.clear();
      newsLastestList.clear();

      final response =
          await HttpService.callAPIjwt("POST", Endpoints.getNews, {});

      if (response['status'] == 200 && response['result'].length > 0) {
        for (var i = 0; i < response['result'].length; i++) {
          NewModel newModel = NewModel(
              running: response['result'][i]['running'].toString(),
              headerNews: response['result'][i]['header_news'],
              subHeaderNews: response['result'][i]['sub_header_news'],
              bodyNews: response['result'][i]['body_news'].toString(),
              urlImgNews: response['result'][i]['url_img_news'],
              dateNews: response['result'][i]['date_news'],
              statusShow: true);
          newsList.add(newModel);
          if (i < 3) {
            newsLastestList.add(newModel);
          }
          update();
        }
      } else {
        debugPrint("error ไม่พบข้อมูลข่าวสาร");
        // getNewsPromotion();
      }
    } catch (e) {
      print('Error: $e');
    }
  }

  parseHtmlString(String htmlString) {
    var document = parse(htmlString);
    String parsedString = parse(document.body!.text).documentElement!.text;
    return parsedString;
  }
}
