import 'dart:async';

import 'package:AAMG/controller/contract/contractlist.controller.dart';
import 'package:AAMG/controller/profile/profile.controller.dart';
import 'package:AAMG/service/http_service.dart';
import 'package:AAMG/view/componance/utils/AppImageAssets.dart';
import 'package:AAMG/service/AppUrl.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
// import 'package:image_gallery_saver/image_gallery_saver.dart';
import 'package:image_gallery_saver_plus/image_gallery_saver_plus.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:dio/dio.dart';
import 'package:sentry/sentry.dart';

import '../../models/contract/myloan_model.dart';
import '../../service/AppService.dart';
import '../../view/componance/AppLoading.dart';
import '../../service/endpoint.dart';
import '../../view/screen/loan_screen/myloan_detail_screen.dart';
import '../../view/screen/loan_screen/webviewContractdigi.dart';

class MyloanController extends GetxController {
  RxString? selectedLoan = ''.obs;
  RxString? contractName = ''.obs;
  RxBool? notPayDetail = true.obs;
  RxInt? indexMyloan = 0.obs;

  RxList<ResponseMyLoan> myLoanList = <ResponseMyLoan>[].obs;
  RxList<ResponseItemLastPay> lastPayList = <ResponseItemLastPay>[].obs;
  RxList? nextpaydigilist = [].obs;
  RxList? nodigilist = [].obs;
  RxList? due_datedigilist = [].obs;

  RxString? selected_bill = ''.obs;

  RxString qrNextpayUrl = ''.obs;
  RxString qrNextpayData = ''.obs;
  RxString qrDateExpired = ''.obs;
  RxString count_qrExpired = ''.obs;
  RxString? timeQRDataExpired = ''.obs;
  RxString? Qrpath = RxString('');

  //TODO กำหนดเวลานับถอยหลัง
  Duration _remaining = const Duration();

  RxList payedGuaranteeList = [
    AppImageAssets.aam_car_payed,
    AppImageAssets.aam_motocycle_payed,
    AppImageAssets.aam_truck_payed,
    AppImageAssets.aam_land_payed,
  ].obs;

  RxList unPayedGuaranteeListRafco = [
    AppImageAssets.rafco_car_payed,
    AppImageAssets.rafco_motocycle_payed,
    AppImageAssets.rafco_truck_payed,
    AppImageAssets.rafco_land_payed,
  ].obs;

  @override
  void onInit() {
    // TODO: implement onInit
    super.onInit();
  }

  void setDataMyLoan(context, String ctt_code) {
    try {
      selectedLoan!.value = ctt_code;
      update();
      // getLoanDetail(context);
      Get.to(
        MyloanDetailScreen(),
      );
    } catch (e) {
      if (kDebugMode) {
        print(e);
        Get.snackbar("error", 'เกิดข้อผิดพลาด  set loan data');
        Get.snackbar("error", e.toString());
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด set loan data",
        duration: Duration(seconds: 3),
      );
    }
  }

  void setDataMyLoanOtherPage(context, String ctt_code) {
    selectedLoan!.value = ctt_code;
    update();
  }

  Future<dynamic> getLoanDetail(context) async {
    try {
      lastPayList.clear();
      myLoanList.clear();
      nextpaydigilist!.clear();
      nodigilist!.clear();
      due_datedigilist!.clear();
      update();

      Map data = {
        // "ctt_code": "BUKBUKMG2LN0722401020",
        "ctt_code": selectedLoan!.value ?? "",
        "phone": Get.find<ProfileController>()
            .profile
            .value
            .phoneFirebase
            .toString(),
      };

      debugPrint("data : $data");
      AppLoading.loadingVerify(context);
      final response = await HttpService.callAPIjwt(
          "POST", Endpoints.getContractDetail, data);
      AppLoading.Loaderhide(context);

      // print("response : $response");

      if (response['status'] == 200) {
        //TODO : ส่งข้อมูลไปหน้า MyloanDetailScreen ด้วย
        ResponseMyLoan myloan = ResponseMyLoan(
          ctt_code: response['result']['item_Myloan'][0]['ctt_code'],
          guarantee_id: response['result']['item_Myloan'][0]['guarantee_id'],
          guarantee_type:
              response['result']['item_Myloan'][0]['guarantee_type'].toString(),
          guarantee_type_name: response['result']['item_Myloan'][0]
              ['guarantee_type_name'],
          fullname: response['result']['item_Myloan'][0]['fullname'],
          data: response['result']['item_Myloan'][0]['data'],
          mn_total: response['result']['item_Myloan'][0]['mn_total'],
          mn_totalInt: response['result']['item_Myloan'][0]['mn_totalInt'],
          payed: response['result']['item_Myloan'][0]['payed'],
          remain: response['result']['item_Myloan'][0]['remain'],
          payedInt: response['result']['item_Myloan'][0]['payedInt'],
          remainInt: response['result']['item_Myloan'][0]['remainInt'],
          nextpay: response['result']['item_Myloan'][0]['nextpay'],
          nextpayQR: response['result']['item_Myloan'][0]['nextpayQR'],
          due_date: response['result']['item_Myloan'][0]['due_date'],
          from: response['result']['item_Myloan'][0]['from'],
          branchPICO: response['result']['item_Myloan'][0]['branchPICO'],
        );
        myLoanList.add(myloan);
        update();

        //TODO ข้อมูลสัญญาสินเชื่อ AAMPAY จาก ctt code
        if (myLoanList[0].ctt_code.obs.string.substring(6, 8) == "DT" || myLoanList[0].ctt_code.obs.string.substring(6, 8) == "DL") {
          if(myLoanList[0].ctt_code.obs.string.substring(6, 8) == "DT") {
            contractName!.value = "สินเชื่อ AAMPAY";
          }else{
            contractName!.value = "RPLC Pay";
          }
          await getCreditDigi();
          nextpaydigilist!.value =
              response["result"]['item_Myloan'][0]["nextpaydigi"];
          nodigilist!.value = response["result"]['item_Myloan'][0]["nodigi"];
          due_datedigilist!.value =
              response["result"]['item_Myloan'][0]["due_datedigi"];
        } else {
          //TODO get ข้อมูลสินเชื่ออื่นๆ
          print("สินเชื่ออื่นๆ");
          await getCredit();
        }

        // print(response['result']['item_payAll'].length);

        //TODO check รายการชำระ
        if (response['result']['item_payAll'].length > 0) {
          for (var i = 0; i < response['result']['item_payAll'].length; i++) {
            ResponseItemLastPay lastPay = ResponseItemLastPay(
              pay_date: response['result']['item_payAll'][i]['pay_date'],
              pay_time: response['result']['item_payAll'][i]['pay_createtime'] ?? "00:00",
              pay_amount: response['result']['item_payAll'][i]['pay_amount'],
              pay_details: response['result']['item_payAll'][i]['pay_details'],
              pay_url: response['result']['item_payAll'][i]['pay_url'],
              pay_date_TH: response['result']['item_payAll'][i]['pay_date_TH'],
            );
            lastPayList.add(lastPay);
          }
          update();
        } else {
          notPayDetail!.value = false;
        }
        update();

        // print("myLoanList : ${lastPayList.length}");

        return true;
        // Get.to(() => MyloanDetailScreen(), arguments: response['result']);
      } else {
        Get.snackbar("error", 'เกิดข้อผิดพลาด ไม่พบข้อมูลสัญญา');
        return false;
      }
    } catch (exception, stackTrace) {
      await Sentry.captureException(
        exception,
        stackTrace: stackTrace,
      );
      if (kDebugMode) {
        print(exception);
        Get.snackbar("error", 'เกิดข้อผิดพลาด');
        Get.snackbar("error", exception.toString());
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
      return false;
    }
  }

  Future<dynamic> getLoanDetailForBillPayment(context) async {
    try {

      final ContractListController contractListCtl =
      Get.find<ContractListController>();

      selectedLoan!.value = await
          contractListCtl.contractList[indexMyloan!.value].ctt_code.toString();
      update();


      lastPayList.clear();
      myLoanList.clear();
      nextpaydigilist!.clear();
      nodigilist!.clear();
      due_datedigilist!.clear();
      update();

      Map data = {
        // "ctt_code": "BUKBUKMG2LN0722401020",
        "ctt_code": selectedLoan!.value ?? "",
        "phone": Get.find<ProfileController>()
            .profile
            .value
            .phoneFirebase
            .toString(),
      };

      // debugPrint("data : $data");
      final response = await HttpService.callAPIjwt(
          "POST", Endpoints.getContractDetail, data);

      // debugPrint("response : $response");

      if (response['status'] == 200) {
        //TODO : ส่งข้อมูลไปหน้า MyloanDetailScreen ด้วย
        ResponseMyLoan myloan = ResponseMyLoan(
          ctt_code: response['result']['item_Myloan'][0]['ctt_code'],
          guarantee_id: response['result']['item_Myloan'][0]['guarantee_id'],
          guarantee_type:
          response['result']['item_Myloan'][0]['guarantee_type'].toString(),
          guarantee_type_name: response['result']['item_Myloan'][0]
          ['guarantee_type_name'],
          fullname: response['result']['item_Myloan'][0]['fullname'],
          data: response['result']['item_Myloan'][0]['data'],
          mn_total: response['result']['item_Myloan'][0]['mn_total'],
          mn_totalInt: response['result']['item_Myloan'][0]['mn_totalInt'],
          payed: response['result']['item_Myloan'][0]['payed'],
          remain: response['result']['item_Myloan'][0]['remain'],
          payedInt: response['result']['item_Myloan'][0]['payedInt'],
          remainInt: response['result']['item_Myloan'][0]['remainInt'],
          nextpay: response['result']['item_Myloan'][0]['nextpay'],
           nextpayQR: response['result']['item_Myloan'][0]['nextpayQR'],
          due_date: response['result']['item_Myloan'][0]['due_date'],
          from: response['result']['item_Myloan'][0]['from'],
          branchPICO: response['result']['item_Myloan'][0]['branchPICO'],
        );
        myLoanList.add(myloan);
        update();

        //TODO ข้อมูลสัญญาสินเชื่อ AAMPAY จาก ctt code
        if (myLoanList[0].ctt_code.obs.string.substring(6, 8) == "DT") {
          contractName!.value = "สินเชื่อ AAMPAY";
          await getCreditDigi();
          nextpaydigilist!.value =
          response["result"]['item_Myloan'][0]["nextpaydigi"];
          nodigilist!.value = response["result"]['item_Myloan'][0]["nodigi"];
          due_datedigilist!.value =
          response["result"]['item_Myloan'][0]["due_datedigi"];
        } else {
          //TODO get ข้อมูลสินเชื่ออื่นๆ
          print("สินเชื่ออื่นๆ");
          await getCredit();
        }

        // print(response['result']['item_payAll'].length);

        //TODO check รายการชำระ
        if (response['result']['item_payAll'].length > 0) {
          for (var i = 0; i < response['result']['item_payAll'].length; i++) {
            ResponseItemLastPay lastPay = ResponseItemLastPay(
              pay_date: response['result']['item_payAll'][i]['pay_date'],
              pay_amount: response['result']['item_payAll'][i]['pay_amount'],
              pay_details: response['result']['item_payAll'][i]['pay_details'],
              pay_url: response['result']['item_payAll'][i]['pay_url'],
              pay_date_TH: response['result']['item_payAll'][i]['pay_date_TH'],
            );
            lastPayList.add(lastPay);
          }
          update();
        } else {
          notPayDetail!.value = false;
        }
        update();

        // print("myLoanList : ${lastPayList.length}");

        return true;
        // Get.to(() => MyloanDetailScreen(), arguments: response['result']);
      } else {
        Get.snackbar("error", 'เกิดข้อผิดพลาด ไม่พบข้อมูลสัญญา');
        return false;
      }
    } catch (exception, stackTrace) {
      // await Sentry.captureException(
      //   exception,
      //   stackTrace: stackTrace,
      // );
      // if (kDebugMode) {
      //   print(exception);
      //   Get.snackbar("error", 'เกิดข้อผิดพลาด');
      //   Get.snackbar("error", exception.toString());
      // }
      // const GetSnackBar(
      //   title: "เกิดข้อผิดพลาด",
      //   message: "เกิดข้อผิดพลาด",
      //   duration: Duration(seconds: 3),
      // );
      // return false;
    }
  }


  Future<dynamic> getCredit() async {
    try {
      Credit credit = Credit.fromJson({
        "guarantee_id": myLoanList[0].guarantee_id.obs.string,
        "ctt_code": myLoanList[0].ctt_code.obs.string,
      });

      final response = await HttpService.post(Endpoints.getCreditByGuaranteeID,
          credit.toJson(), HttpService.noKeyRequestHeaders);

      debugPrint(response.toString());

      // if (response["status"] == 200 && response["show"] == "true") {
      //   money_approve!.value = response["money_approve"].toString();
      //   total_loan!.value = response["total_loan"].toString();
      //   remain_loan!.value = response["remain_loan"].toString();
      // } else {
      //   money_approve!.value = response["money_approve"].toString();
      //   total_loan!.value = response["total_loan"].toString();
      //   remain_loan!.value = "0";
      // }
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }
  }

  Future<dynamic> getCreditDigi() async {
    try {
      final ContractListController contractListCtl =
          Get.find<ContractListController>();
      CreditDigi creditDigi = CreditDigi.fromJson({
        "cust_code": contractListCtl.custCode!.value,
      });

      final response = await HttpService.post(
          Endpoints.getCreditByGuaranteeIDDigital,
          creditDigi.toJson(),
          HttpService.noKeyRequestHeaders);

      if (response["status"] == 200) {
        // money_approve!.value = response["money_approve"].toString();
        // total_loan!.value = response["debt_loan"].toString();
        // remain_loan!.value = response["remain_loan"].toString();
        // current_contract!.value = response["current_contract"];
        // type_loan!.value = "digi";
      }
      update();
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }
  }

  void setIndexMyloan(int index) {
    indexMyloan!.value = index;
    update();
    debugPrint("#### indexMyloan : ${indexMyloan!.value}");
  }

  Future<void> checkLoanDetail(context) async {
    try {
      final ContractListController contractListCtl =
          Get.find<ContractListController>();

      selectedLoan!.value =
          contractListCtl.contractList[indexMyloan!.value].ctt_code.toString();
      update();

      var chk = await getLoanDetail(context);

      return chk;
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }
  }

  Future<dynamic> setDataPayment(
      int index, String ctt_code, String nextpay) async {
    try {
      final ContractListController contractListCtl =
          Get.find<ContractListController>();
      // print("ctt_code : $ctt_code");
      // print("nextpay : $nextpay");

      if (_remaining.inSeconds == 0) {
        startTimer();
      }

      //TODO gen qr code
      getQrCode();
      qrNextpayData.value =
          contractListCtl.contractList[index].nextpay.toString();
      update();
    } catch (e) {
      if (kDebugMode) {
        print('setDataPayment : $e');
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }
  }

  void startTimer() {
    if (_remaining.inSeconds == 0) {
      // print('#######');
      _remaining = Duration(minutes: 10); // ตั้งเวลา 10 นาที
    }
    Timer timer = Timer.periodic(Duration(seconds: 1), (timer) {
      _remaining -= Duration(seconds: 1);
      update(); // Update UI using GetX update()
      formatDuration(_remaining);
      // print('remaining : $_remaining');

      DateTime now = getTime();
      // บวกเวลา 10 นาที สำหรับโชว์ หน้า UI
      DateTime tenMinutesLater = addMinutes(now, 10);
      extractDateAndTime(tenMinutesLater.toString());

      if (_remaining.inSeconds <= 0) {
        timer.cancel();
        print('เวลาหมด!');
      }
    });
  }

  DateTime getTime() {
    return DateTime.now(); // ดึงเวลาปัจจุบัน
  }

  DateTime addMinutes(DateTime dateTime, int minutes) {
    return dateTime.add(Duration(minutes: minutes)); // บวกเวลาเพิ่ม
  }

  String extractDateAndTime(String dateTimeString) {
    // แปลง dateTimeString เป็น DateTime
    DateTime dateTime = DateTime.parse(dateTimeString);

    // แยกวันที่
    String formattedDate =
        '${dateTime.day.toString().padLeft(2, '0')}/${dateTime.month.toString().padLeft(2, '0')}/${dateTime.year}';

    // แยกเวลา
    String formattedTime =
        extractTime2(dateTimeString); // เรียกใช้ฟังก์ชันจากคำตอบก่อนหน้า

    // รวมวันที่และเวลา
    String formattedDateTime = '$formattedDate, $formattedTime';
    timeQRDataExpired!.value = formattedDateTime;
    update();
    return formattedDateTime;
  }

  String extractTime(String dateTimeString) {
    // แปลง dateTimeString เป็น DateTime
    DateTime dateTime = DateTime.parse(dateTimeString);

    // แยกชั่วโมง นาที และวินาที
    int hour = dateTime.hour;
    int minute = dateTime.minute;

    // ฟอร์แมตเวลาเป็น HH:mm
    String formattedTime =
        '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}';
    return formattedTime;
  }

  String extractTime2(String dateTimeString) {
    // แปลง dateTimeString เป็น DateTime
    DateTime dateTime = DateTime.parse(dateTimeString);

    // แยกชั่วโมง นาที และวินาที
    int hour = dateTime.hour;
    int minute = dateTime.minute;

    // ฟอร์แมตเวลาเป็น HH:mm
    String formattedTime =
        '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}';
    return formattedTime;
  }

  formatDuration(Duration duration) async {
    // แปลงวินาทีเป็นนาที
    String minutes = duration.inMinutes.toString().padLeft(2, '0');

    // แปลงวินาทีที่เหลือเป็นวินาที
    int secondsRemaining = duration.inSeconds % 60;
    String seconds = secondsRemaining.toString().padLeft(2, '0');

    // แปลงวินาทีที่เหลือเป็นเศษส่วนของวินาที
    int milliseconds = duration.inMilliseconds % 1000;
    String millisecondsString = milliseconds.toString().padLeft(2, '0');
    // print('$minutes:$seconds:$millisecondsString');
    count_qrExpired.value = '$minutes:$seconds:$millisecondsString';
    update();
    // print('count_qrExpired : ${count_qrExpired.value}');
    return '$minutes:$seconds:$millisecondsString';
  }

  Future<dynamic> getQrCode() async {
    try {
      final ProfileController profileCtl = Get.find<ProfileController>();
      qrNextpayUrl.value =
          'https://mapp-app.s3.ap-southeast-1.amazonaws.com/MappAAM/qrSPS/MappAAM-aDUEXQ8yqWNvquRRFQbZ.png';
      // Map data = {
      //   "from": myLoanList[0].from.obs.string,
      //   "amount": myLoanList[0].nextpayQR.obs.string,
      //   "ctt_code":  myLoanList[0].ctt_code.obs.string,
      //   // "phone": "0806736956",
      //   "phone": profileCtl.profile.value.phone.obs.string,
      //   "branchPICO": myLoanList[0].branchPICO!.obs.string,
      // };
      //
      // // final response = await HttpService.post(Endpoints.qrCodePayUrl, data, HttpService.noKeyRequestHeaders)
      //
      // if (response['statusCode'] == 200) {
      //   // qrCode!.value = response['qrcode'];
      //   qrNextpayUrl!.value = response["url"];
      //   // isShowQr!.value = true;
      //   update();
      // } else {
      //   // isShowQr!.value = false;
      //   /// alertDialogV3(context, "แจ้งเตือน", "เกิดข้อผิดพลาด");
      //   update();
      // }
    } catch (exception, stackTrace) {
      await Sentry.captureException(
        exception,
        stackTrace: stackTrace,
      );
      if (kDebugMode) {
        print('setDataPayment : $exception');
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }
  }

  Future<dynamic> saveImageQrCode() async {
    try {
      Map<Permission, PermissionStatus> statuses = await [
        Permission.storage,
      ].request();

      if (await Permission.storage.request().isGranted) {
        // isLoader!.value = true;
        // update();
        var response = await Dio().get(qrNextpayUrl!.value,
            options: Options(responseType: ResponseType.bytes));

        final result = await ImageGallerySaverPlus.saveImage(
            Uint8List.fromList(response.data),
            quality: 60,
            name: "AAMQRCode_${DateTime.now().millisecondsSinceEpoch}.png");
        print(result);

        if (result != null) {
          Get.snackbar("แจ้งเตือน", "บันทึกสำเร็จ");
        } else {
          Get.snackbar("แจ้งเตือน", "ไม่สามารถบันทึกได้");
        }
      } else {
        Get.snackbar("แจ้งเตือน", "ไม่สามารถบันทึกได้");
      }
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }
  }

  String setPayedDateLocale(String dueDate, String lang)  {
    //TODO ฟังก์ชันปรับ format วันที่ ตามภาษาในเมนู setting ex. th, en ,la , km
    try {

      print(dueDate);
      print('pay_time : ${lang}');
      var date_localeString = "";
      DateTime date = DateTime.parse(dueDate.toString());

      date_localeString = AppService.dateLocalized(date, lang);

      if (date_localeString.toString().isEmpty) {
        debugPrint("Error : date locale String is empty");
        date_localeString = dueDate;
      }
      print(date_localeString);
      return date_localeString;
    } catch (e) {
      if (kDebugMode) {
        debugPrint("Error : function convert date locale String");
        debugPrint(e.toString());
      }
      return dueDate;
    }
  }


  Future<void> setBillUrl(int index) async {
    selected_bill!.value = lastPayList[index].pay_url.toString();
    update();
  }

  void handleURLButtonPress(
       context, String url, String textappbar) {
    Get.to(WebViewContractDigi(url, textappbar));
  }
}
