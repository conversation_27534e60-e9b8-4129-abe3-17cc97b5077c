import 'dart:convert';
import 'dart:io';
import 'dart:io' show Platform;
import 'package:AAMG/controller/chatinapp/chatinapp.controller.dart';
import 'package:AAMG/controller/chatinapp/register.TG.controller.dart';
import 'package:AAMG/controller/contract/contractlist.controller.dart';
import 'package:AAMG/controller/kyc/kyc.controller.dart';
import 'package:AAMG/controller/likepoint/webview.point.controller.dart';
import 'package:AAMG/controller/mr/mr.controller.dart';
import 'package:AAMG/controller/notification/notification.controllet.dart';
import 'package:AAMG/controller/register/registerAddress.controller.dart';
import 'package:AAMG/controller/session/session.controller.dart';
import 'package:AAMG/controller/transalation/translation_key.dart';
import 'package:AAMG/view/componance/themes/theme.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_rx/get_rx.dart';
import 'package:get/get_rx/src/rx_types/rx_types.dart';
import 'package:get_storage/get_storage.dart';
import 'package:AAMG/controller/AppConfigService.dart';

import '../../models/profile_model.dart';
import '../../service/http_service.dart';
import '../../service/AppService.dart';
import '../../service/endpoint.dart';
import '../likepoint/likepoint.controller.dart';

class ProfileController extends GetxController {
  Rx<Profile> profile = Profile().obs;
  RxDouble percentUpdateProfile = 0.0.obs;
  RxString percentUpdateProfileText = RxString('');
  RxBool? checkPolicy = true.obs;
  RxString? updatePolicy = ''.obs;
  RxString? linkImage = ''.obs;
  RxString? numHome = ''.obs;
  RxString? numMoo = ''.obs;
  BuildContext? context;
  RxBool isTop = false.obs;
  RxBool isShow = false.obs;
  RxBool? isMember = false.obs;
  RxString member_id = RxString('');
  RxString member_email = RxString('');

  GetStorage storage = GetStorage();

  List popUpTextSet = [
    {
      'title': popUpTutorialSettingPage.tr,
      'content': popUpTutorialSettingPage1.tr,
      'content1': popUpTutorialSettingPage2.tr,
    },
    {
      'title': popUpTutorialUpdateProfile.tr,
      'content': '',
      'content1': '',
    },
  ];
  List popUpMas = [
    'assets/tutorial/aam/mas_aam3.png',
    'assets/tutorial/aam/mas_aam5.png'
  ];
  List popUpMasRafco = [
    'assets/tutorial/rafco/mas_rafco3.png',
    'assets/tutorial/rafco/mas_rafco5.png'
  ];
  List popUpMasRplc = [
    'assets/tutorial/rplc/mas_rplc3.png',
    'assets/tutorial/rplc/mas_rplc5.png'
  ];
  RxInt indexPopupSetting = 0.obs;
  RxInt? indexPageSetting = 0.obs;

  @override
  void onInit() {
    // TODO: implement onInit
    super.onInit();
    // getProfile();
  }

  setIndexPopupSetting(int index) {
    indexPopupSetting.value = index;
    print('indexPopupSetting: $indexPopupSetting');
    update();
  }

  void test() {
    AppConfigService appConfigService = Get.find<AppConfigService>();
    print(appConfigService.configCloudflareAPI);
  }

  Future<dynamic> getProfile() async {
    try {
      var phone_firebase = await storage.read("phone_firebase");

      Map data = {"phone_firebase": phone_firebase};

      final response =
          await HttpService.callAPIjwt("POST", Endpoints.getDataCust, data);

      print('getProfile response: $response');

      if (response['status'] == 200) {
        profile.value = Profile(
          running: response['result']['running'].toString(),
          phone: response['result']['phone'].toString(),
          address: response['result']['Address'],
          tumbol: response['result']['Tumbol'],
          amphur: response['result']['Amphur'],
          province: response['result']['Province'],
          zipcode: response['result']['Zipcode'].toString(),
          firstname: response['result']['firstname'],
          lastname: response['result']['lastname'],
          email: response['result']['email'],
          idcard: response['result']['idcard'].toString(),
          avatar: response['result']['avatar'],
          username: response['result']['username'],
          phoneFirebase: response['result']['phone_firebase'],
          birthday: response['result']['birthday'],
          displayName: response['result']['displayName'],
          app_agree_status: response['result']['app_agree_status'],
          privacy_agree_status: response['result']['privacy_agree_status'],
          loan_agree_status: response['result']['loan_agree_status'],
          digital_agree_status: response['result']['digital_agree_status'],
          ref_code: response['result']['ref_code'],
          ref_code_other: response['result']['ref_code_other'],
          branch_name: response['result']['branch_name'],
          aicp_agree_status: response['result']['aicp_agree_status'],
          aicp_privacy_status: response['result']['aicp_privacy_status'],
          fb_uid: response['result']['fb_uid'],
          line_uid: response['result']['line_uid'],
          apple_uid: response['result']['apple_uid'],
          google_uid: response['result']['google_uid'],
          tg_uid: response['result']['tg_uid'],
          wa_uid: response['result']['wa_uid'],
          facebook_connect: response['result']['facebook_connect'],
          line_connect: response['result']['line_connect'],
          apple_connect: response['result']['apple_connect'],
          google_connect: response['result']['google_connect'],
          tg_connect: response['result']['tg_connect'],
          wa_connect: response['result']['wa_connect'],
        );
        update();
        debugPrint('profile: ${Profile().toJson()}');
        debugPrint('getProfile: ${profile.value.firstname}');
        Get.put(ContractListController()).customerContactList();
        checkAcceptAppPolicy();
        calculateUpdateProfileData();
        updateFCMToken();
        print('calculateUpdateProfileData pass');
        final WebViewPointController LP2_Ctl =
            Get.find<WebViewPointController>();
        LP2_Ctl.Loading(true);
        //TODO set data for open Likepoint2.0
        setDataLikepoint();
        //TODO check Migrate Point
        if (LP2_Ctl.balanceLikePoint.value.toString().isNotEmpty &&
            LP2_Ctl.isAlreadyMigrated.value == true) {
          debugPrint("Already checked Migrate Point");
          LP2_Ctl.Loading(false);
        } else {
          debugPrint("Not check Migrate Point");
          LP2_Ctl.initializeData();
        }

        if (appConfigService.countryConfigCollection.toString() == "aam") {
          Get.find<ContractListController>().checkBu('aam');
          Get.find<RegisterTGController>().changeData('aam');
          Get.find<ChatInAppController>().changeDataBu('aam');
        } else if (appConfigService.countryConfigCollection.toString() ==
            "rafco") {
          Get.find<ContractListController>().checkBu('rafco');
          Get.find<RegisterTGController>().changeData('rafco');
          Get.find<ChatInAppController>().changeDataBu('rafco');
        } else {
          Get.find<ContractListController>().checkBu('rplc');
          Get.find<RegisterTGController>().changeData('rplc');
          Get.find<ChatInAppController>().changeDataBu('rplc');
        }
        Get.find<LikePointController>()
            .getBalanceByPhoneNumber(); //TODO get balance Likepoint1.0
        Get.find<ContractListController>().getContactStatus();
        Get.find<MRController>().getMRData().then((value) async {
          Get.find<MRController>().setInitialRefCode();
          await Get.find<MRController>().getMRReferralDownload();
        });
        Get.find<RegisterAddressController>().checkConfigAddress();
        // Get.find<RegisterAddressController>().districtsData;

        print(
            'getProfile: ${appConfigService.countryConfigCollection.toString()}');
        if (appConfigService.countryConfigCollection.toString() == "aam" || appConfigService.countryConfigCollection.toString() == "rplc") {
          Get.find<KYCController>().checkstatusSumSub();
          Get.find<KYCController>().checkstatusbookbank();
        }
        checkMemberUser();
        // checkPolicy();
        // Get.find<NotificationController>().getNotification(context);
      } else {
        print('error: ไม่พบข้อมูล');
        // Get.put(SessionController())
        //               .logout(Get.context!);
      }
    } catch (e) {
      print('getProfile error');
      debugPrint(e.toString());
          // Get.put(SessionController())
          //             .logout(Get.context!);
    }
  }

  separateAddress() {
    try {
      if (profile.value.address.toString().isNotEmpty ||
          profile.value.address.toString().toLowerCase() != 'null' ||
          profile.value.address != null) {
        debugPrint('address is not empty');
        var add = profile.value.address.toString();
        var parts = add.split(" ");
        numHome!.value = parts[0];
        numMoo!.value = parts[1];
        update();
      } else {
        debugPrint('address is empty');
        numHome!.value = '';
        numMoo!.value = '';
        update();
      }
    } catch (e) {
      debugPrint('separateAddress -> ${e}');
      numHome!.value = '';
      numMoo!.value = '';
      update();
    }
  }

  void setDataLikepoint() {
    final WebViewPointController webViewPointCtl =
        Get.put(WebViewPointController());
    // webViewPointCtl.checkMigrate(profile.value.phoneFirebase.obs.string.isEmpty
    //     ? AppService.phoneToPhoneCode(profile.value.phone.obs.string)
    //     : profile.value.phoneFirebase.obs
    //         .string); //TODO check migrate Likepoint1.0 -> Likepoint2.0.
    webViewPointCtl.setUserData(
        profile.value.firstname.toString(),
        profile.value.lastname.toString(),
        profile.value.phoneFirebase.obs.string.isEmpty
            ? AppService.phoneToPhoneCode(profile.value.phone.obs.string)
            : profile.value.phoneFirebase.obs.string); //TODO set personal data.
    webViewPointCtl.setPhoneEncode(
        profile.value.phoneFirebase.obs.string.isEmpty
            ? AppService.phoneToPhoneCode(profile.value.phone.obs.string)
            : profile.value.phoneFirebase.obs
                .string); //TODO เข้ารหัสเบอร์สำหรับเปิดหน้าเว็ป.
  }

  Future<void> checkAcceptAppPolicy() async {
    try {
      AppConfigService appConfigService = Get.find<AppConfigService>();

      // if (appConfigService.countryConfigCollection.toString() == "aam") {
      if (profile.value.app_agree_status.toString() == "Y" ||
          profile.value.privacy_agree_status.toString() == "Y") {
        checkPolicy!.value = true;
      } else {
        // await AAMPolicy.alertTermAndPolicy(context);
        checkPolicy!.value = false;
        updatePolicy!.value = "true";
      }
      update();
      // } else {
      //   checkPolicy!.value = true;
      //   update();
      // }
    } catch (e) {
      debugPrint(e.toString());
      checkPolicy!.value = false;
      updatePolicy!.value = "true";
      update();
    }
  }

  Future<double> calculateUpdateProfileData() async {
    try {
      // Calculate overall update progress
      double totalProgress = 0.0;
      int totalFields = 6; // Assuming 6 profile fields (modify as needed)

      // Check each field with improved null safety and cleaner logic
      if (profile.value.phone?.isNotEmpty == true) {
        totalProgress += 1.0 / totalFields;
      }
      if (profile.value.firstname?.isNotEmpty == true &&
          profile.value.lastname?.isNotEmpty == true) {
        totalProgress += 1.0 / totalFields;
      }
      if (profile.value.email?.isNotEmpty == true) {
        totalProgress += 1.0 / totalFields;
      }
      if (profile.value.idcard?.isNotEmpty == true) {
        totalProgress += 1.0 / totalFields;
      }
      if (profile.value.address?.isNotEmpty == true &&
          profile.value.amphur?.isNotEmpty == true &&
          profile.value.province?.isNotEmpty == true) {
        totalProgress += 1.0 / totalFields;
      }
      if (profile.value.avatar?.isNotEmpty == true) {
        totalProgress += 1.0 / totalFields;
      }

      // Update progress state and text
      percentUpdateProfile.value = totalProgress;
      percentUpdateProfileText.value = (totalProgress * 100).toInt().toString();
      update();

      return totalProgress;
    } catch (e) {
      print('calculateUpdateProfileData error');
      debugPrint(e.toString());
      return 0.0;
    }
  }

  changeImg(url) async {
    Map data = {
      'phone_firebase': profile.value.phoneFirebase.toString(),
      'type': 'img_profile',
      'data': {
        'img': url,
      },
    };
    print('data: $data');

    final response =
        await HttpService.callAPIjwt("POST", Endpoints.updateProfile, data);
    print('response: $response');
    // int status = response['statusCode'];
    if (response['status'] == 200) {
      linkImage?.value = url;
      // profile.value.avatar = linkImage?.value;
      // print(profile.value.avatar.toString());
    }
    getProfile();
    update();
    // return profile.value.avatar;
  }
  Future<dynamic> updateFCMToken() async {
    try {
      print('updateFCMToken Here');
      var fcmToken = await storage.read("tokenNotify");
      print('fcmToken : $fcmToken');

      String currentOS = getCurrentPlatform();
      print('Current OS: $currentOS');

      Map data = {
        "phone_firebase": profile.value.phoneFirebase.toString(),
        "token": fcmToken,
        "os": currentOS,
      };

      final response =
      await HttpService.callAPIjwt("POST", Endpoints.updateFCMToken, data);

      print('updateFCMToken response: $response');

      if (response['status'] == 200) {
        print('✅ updateFCMToken: success');
      } else {
        print('❌ updateFCMToken: failed');
      }
    } catch (e) {
      print('❌ updateFCMToken error');
      debugPrint(e.toString());
    }
  }

  String getCurrentPlatform() {
    if (kIsWeb) return "web";
    if (Platform.isAndroid) return "android";
    if (Platform.isIOS) return "ios";
    if (Platform.isMacOS) return "macos";
    if (Platform.isWindows) return "windows";
    if (Platform.isLinux) return "linux";
    return "unknown";
  }



  Future<dynamic> checkMemberUser() async {
    try {
      Map data = {
        // "phone_firebase": "+66927251261"
        "phone_firebase": profile.value.phoneFirebase
      };

      debugPrint('checkMemberUser -> ${data}');

      final response =
          await HttpService.callAPICloudflare("POST", "loginWithPhone", data);
      // debugPrint('checkMemberUser response -> ${response}');

      if (response["status"] == 200) {
        // debugPrint('checkMemberUser -> ${response['status']}');
        isMember!.value = true;
        member_id.value =
            base64Encode(utf8.encode(response['result'][0]['id'].toString()));
        member_email.value = base64Encode(
            utf8.encode(response['result'][0]['email1'].toString()));
        update();
        // return response;
      } else {
        // debugPrint('checkMemberUser -> ${response['status']}');
        isMember!.value = false;
        update();
      }
    } catch (e) {
      debugPrint('checkMemberUser -> ${e}');
    }
  }

  Future<void> updateRefcodeData(String data) async {
    try {
      // print('updateRefcodeData -> ${data}');
      profile.value.ref_code_other!.obs.value = data ?? '';
      update();
    } catch (e) {
      debugPrint('updateRefcodeData -> ${e}');
    }
  }
}