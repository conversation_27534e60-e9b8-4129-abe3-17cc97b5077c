import 'dart:convert';
import 'dart:io';
import 'dart:ui' as ui;
import 'package:AAMG/controller/transalation/translation_key.dart';
import 'package:AAMG/view/componance/AppLoading.dart';
import 'package:AAMG/view/componance/utils/AppImageAssets.dart';
import 'package:AAMG/view/componance/utils/AppSvgImage.dart';
import 'package:AAMG/view/screen/home/<USER>';
import 'package:flutter/rendering.dart';
import 'package:get/state_manager.dart';
import 'package:get_storage/get_storage.dart';
import 'package:http/http.dart' as http;
import 'package:AAMG/controller/AppConfigService.dart';
import 'package:AAMG/controller/register/register.controller.dart';
import 'package:AAMG/controller/register/registerAddress.controller.dart';
import 'package:AAMG/controller/request_loan/loan.controller.dart';
import 'package:AAMG/view/screen/mr/mr_register_success.dart';
import 'package:AAMG/view/screen/mr/referfriend_success.dart';
import 'package:AAMG/view/screen/mr/success_addreferalcode.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:AAMG/controller/profile/profile.controller.dart';
import 'package:get/get_rx/get_rx.dart';
import 'package:get/get_rx/src/rx_types/rx_types.dart';
// import 'package:image_gallery_saver/image_gallery_saver.dart';
import 'package:image_gallery_saver_plus/image_gallery_saver_plus.dart';
import 'package:image_picker/image_picker.dart';
import 'package:sentry/sentry.dart';
import 'dart:core';
import '../../app_config.dart';
import '../../models/mr/referral_model.dart';
import '../../models/mr_model.dart';
import '../../models/pop_upModel.dart';
import '../../service/http_service.dart';
import '../../service/endpoint.dart';
import '../../view/componance/themes/theme.dart';
import '../likepoint/webview.point.controller.dart';
import '../notification/notification.controllet.dart';

class MRController extends GetxController {
  Rx<MRResponse> mrData = MRResponse().obs;
  RxList<ReferralMR> mrReferral = <ReferralMR>[].obs;
  RxList<ReferralMR> filtered_mrReferral = <ReferralMR>[].obs;
  Rx<ReferralCode> referralData = ReferralCode().obs;
  List<PopUpModel> popupList = [];
  List<PopUpModel> MRpopupList = [];
  List<PopUpModel> MRPagepopupList = [];
  RxBool isLoadingPopup = false.obs;
  final NotificationController   notiController = Get.find<NotificationController>();
  TextEditingController enterfullnameController = TextEditingController();
  TextEditingController idcardvalueoneController = TextEditingController();
  TextEditingController enteroccupationController = TextEditingController();
  TextEditingController enterphoneController = TextEditingController();
  TextEditingController referCode = TextEditingController();

  /// แนะนำเพื่อน
  Rx<TextEditingController> firstNameController = TextEditingController().obs;
  Rx<TextEditingController> lastNameController = TextEditingController().obs;
  Rx<TextEditingController> phoneController = TextEditingController().obs;

  TextEditingController provinceController = TextEditingController();
  TextEditingController districtController = TextEditingController();
  TextEditingController searchController = TextEditingController();

  RxInt? close = 0.obs;
  RxInt? pending = 0.obs;

  RxBool? checkPolicy = false.obs;
  RxString idMr = ''.obs;
  RxInt? indexPageMR = 0.obs;
  RxBool? isFinalPageMR = false.obs;
  File? imageFile;
  RxList imageList = [].obs;
  RxString? selectedProvince = ''.obs;
  RxString? selectedDistrict = ''.obs;
  RxBool Loading = false.obs;

  String searchQuery = '';
  List filteredUsers = [];
  final box = GetStorage();
  List listHistory = [
    {
      "name": "ปุญญพัตน์",
      "mrNum": "T7HRKV",
      "status": "รอจัดสินเชื่อ",
      'image': ""
    },
    {"name": "สุวัฒน์", "mrNum": "T7H56A", "status": "ปิดได้", 'image': ""},
    {
      "name": "ธนวันต์",
      "mrNum": "@AAMMR001",
      "status": "ปิดไม่ได้",
      'image': ""
    },
    {"name": "มัทนา", "mrNum": "@AAMMR003", "status": "ปิดได้", 'image': ""},
    {
      "name": "ศักดิ์กรินทร์",
      "mrNum": "@AAMMR11",
      "status": "รอจัดสินเชื่อ",
      'image': ""
    },
    {
      "name": "ปุญญพัตน์",
      "mrNum": "T7HRKV",
      "status": "รอจัดสินเชื่อ",
      'image': ""
    },
    {"name": "สุวัฒน์", "mrNum": "T7H56A", "status": "ปิดได้", 'image': ""},
    {
      "name": "ธนวันต์",
      "mrNum": "@AAMMR001",
      "status": "ปิดไม่ได้",
      'image': ""
    },
    {"name": "มัทนา", "mrNum": "@AAMMR003", "status": "ปิดได้", 'image': ""},
    {
      "name": "ศักดิ์กรินทร์",
      "mrNum": "@AAMMR11",
      "status": "รอจัดสินเชื่อ",
      'image': ""
    },
  ];
  AppConfigService appConfig = Get.find<AppConfigService>();

  RxBool? showTutorialMr = true.obs;
  GetStorage storage = GetStorage();
  RxInt indexTutorialMR = 0.obs;
  List popUpText = [
    {
      'title': popUpTutorialIncome.tr,
      'content': '',
      'content1': '',
      'content2': '',
    },
    {
      'title': popUpTutorialSummarize.tr,
      'content': '',
      'content1': '',
      'content2': '',
    },
    {
      'title': popUpTutorialRecommend.tr,
      'content': popUpTutorialRecommend1.tr,
      'content1': popUpTutorialRecommend2.tr,
      'content2': popUpTutorialRecommend3.tr,
    },
  ];

  List masPopUp = [
    'assets/tutorial/aam/mas_aam1.png',
    'assets/tutorial/aam/mas_aam3.png',
    'assets/tutorial/aam/mas_aam5.png'
  ];
  List masPopUpRafco = [
    'assets/tutorial/rafco/mas_rafco1.png',
    'assets/tutorial/rafco/mas_rafco3.png',
    'assets/tutorial/rafco/mas_rafco5.png'
  ];
  List masPopUpRplc = [
    'assets/tutorial/rplc/mas_rplc1.png',
    'assets/tutorial/rplc/mas_rplc3.png',
    'assets/tutorial/rplc/mas_rplc5.png'
  ];

  RxInt? totalPointReferal = 0.obs;
  RxString iconProfileLevel = ''.obs;
  RxList<ReferralDownload> referral_download = <ReferralDownload>[].obs;
  RxList<ReferralDownload> filtered_referral_download =
      <ReferralDownload>[].obs;
  TextEditingController searchloadApp = TextEditingController();
  RxBool isSubmitting = false.obs;

  var hasShownPopup = false;
  @override
  void onInit() {
    // TODO: implement onInit
    super.onInit();

    // filteredUsers = mrReferral;
  }

  setIndexTutorialMR(int index) {
    indexTutorialMR.value = index;
    update();
  }


  bool _isDisposed = false;
  RxBool isLoadMRData = false.obs;

  bool get isActive => !_isDisposed;

  @override
  void onClose() {
    _isDisposed = true;
    enterfullnameController.dispose();
    idcardvalueoneController.dispose();
    enteroccupationController.dispose();
    enterphoneController.dispose();
    referCode.dispose();
    firstNameController.value.dispose();
    lastNameController.value.dispose();
    phoneController.value.dispose();
    super.onClose();
  }







  // void setShowTutorialMr() {
  //   showTutorialMr!.value = true;
  //   storage.write('showTutorialMr', true);
  //   print("showTutorialMr => ${showTutorialMr!.value}");
  //   update();
  //   // Get.to(() => HomeNavigator());
  // }

  // void checkShowTutorialMr() {
  //   storage.read('showTutorialMr') == null
  //       ? showTutorialMr!.value = true
  //       : showTutorialMr!.value =  storage.read('isGuest');
  //   update();
  // }
  Future<dynamic> getMRData() async {
    print('getMRData');
    var mr_status = false;
    try {
      Map data = {
        // "phone_firebase": "000000000000",
        "phone_firebase":
            Get.find<ProfileController>().profile.value.phoneFirebase,
      };

      final response =
          await HttpService.callAPIjwt('POST', Endpoints.getMRData, data);
      // debugPrint("response => $response");
      isLoadMRData.value = true;

      if (response['status'] == 200 && response['result'] == 1) {
        print("ไม่ได้สมัคร MR");
        mr_status = false;
        update();
      } else if (response['status'] == 200 && response['result'].length > 0) {
        print("สมัคร MR");
        mrData.value = MRResponse(
          mr_id: response['result']['mr_id'],
          mr_fname: response['result']['mr_fname'],
          mr_lname: response['result']['mr_lname'],
          mr_phone: response['result']['mr_phone'],
          mr_rank: response['result']['mr_rank'],
        );
        // debugPrint("mrData => ${mrData.value.toJson()}");
        mr_status = true;

        update();
      }
      // if (response['status'] == 200 && response['result'].length > 0) {
      //   mrData.value = MRResponse(
      //     mr_id: response['result']['mr_id'],
      //     mr_fname: response['result']['mr_fname'],
      //     mr_lname: response['result']['mr_lname'],
      //     mr_phone: response['result']['mr_phone'],
      //     mr_rank: response['result']['mr_rank'],
      //   );
      //   print("mrData => ${mrData.value.toJson()}");
      //   update();
      // } else if(response['status'] == 200 && response['result']==1){
      //   print("ไม่ได้สมัคร MR");
      //   update();
      // }
      else {
        mr_status = false;

        debugPrint('No MR data');

      }
      //todo set icon ranking
      sortIconMRRanking();
      await getReferFriend();
    } catch (e) {
      if (kDebugMode) {
        print("error getMRData1 =>$e");
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
      return false;
    }finally{
      await  getDatapop_up(mr_status);
    }
  }



  void markPopupShown() {
    box.write('popup_shown_this_session', true);
  }

  bool shouldShowPopup() {
    return !(box.read('popup_shown_this_session') ?? false);
  }

  Future<void> getDatapop_up(check) async {
print('getDatapop_up');
    // ✅ ตรวจว่าควรแสดง popup หรือไม่
    // final shouldShow = !(box.read('popup_shown_this_session') ?? false);
    // if (!shouldShow) {
    //   print(!shouldShow);
    //   notiController.isPopupVisible.value = false;
    //   return;
    // }
notiController.isPopupVisible.value = false;
isLoadingPopup.value = true;
    final url = Uri.parse('https://n8n-deploy.agilesoftgroup.com/webhook/4ecce034-44c8-48cd-9498-5f5c0033f335');
    final body = {
      "check": check,
      "countryConfigCollection": AppConfig.of(Get.context!).countryConfigCollection.toString()
    };

    try {
      final response = await http.post(
        url,
        headers: {
          "Content-Type": "application/json",
          "Accept": "application/json",
        },
        body: jsonEncode(body),
      );

      // print(response.body);

      if (response.statusCode == 200) {
        final jsonData = jsonDecode(response.body);
        final List<dynamic> result = jsonData[0]['result'];

        popupList = result
            .map((item) => PopUpModel.fromJson(item))
            .where((p) => p.notifyPic != '')
            .where((p) => p.popupType != 'mr_page')
            .toList();

        MRPagepopupList = result
            .map((item) => PopUpModel.fromJson(item))
            .where((p) => p.notifyPic != '')
            .where((p) => p.popupType == 'mr_page')
            .toList();

        check == true ?   MRpopupList = result
            .map((item) => PopUpModel.fromJson(item))
            .where((p) => p.notifyPic != '')
            .where((p) => p.popupScope == 'mr')
            .toList()
        : MRpopupList = [];

        if (popupList.isNotEmpty) {
          notiController.isPopupVisible.value = true;
          box.write('popup_shown_this_session', true); // ✅ mark ว่าแสดงแล้ว
        }
      } else {
        print("❌ ไม่สำเร็จ: ${response.statusCode}");
      }
    } catch (e) {
      print("⚠️ Error: $e");
    } finally {
     isLoadingPopup.value = false;
    }
  }




  void sortIconMRRanking() {
    final appName = appConfigService.countryConfigCollection.toString();
    final rank = mrData.value.mr_rank?.toString().toUpperCase() ?? "CLASSIC";

    // กำหนดค่าเริ่มต้นเป็น Classic หากค่า rank ไม่ถูกต้อง
    final rankKey = ["CLASSIC", "", "NULL"].contains(rank) ? "CLASSIC" : rank;

    // Map สำหรับเชื่อม rank กับ icon
    final iconMap = {
      "CLASSIC": {
        "aam": AppSvgImage.icon_classic_level_aam,
        "rafco": AppSvgImage.icon_classic_level_rafco,
        "rplc": AppSvgImage.icon_classic_level_rplc,
      },
      "GOLD": {
        "aam": AppSvgImage.icon_gold_level_aam,
        "rafco": AppSvgImage.icon_gold_level_rafco,
        "rplc": AppSvgImage.icon_gold_level_rplc,
      },
      "PLATINUM": {
        "aam": AppSvgImage.icon_platinum_level_aam,
        "rafco": AppSvgImage.icon_platinum_level_rafco,
        "rplc": AppSvgImage.icon_platinum_level_rplc,
      }
    };

    // กำหนดค่าเริ่มต้น (Default) ถ้า appName ไม่มีใน Map
    iconProfileLevel.value =
        iconMap[rankKey]?[appName] ?? AppSvgImage.icon_platinum_level_aam;
    update();
  }

  void acceptPolicy() {
    checkPolicy!.value = !checkPolicy!.value;
    update();
  }

  detailMr(id) async {
    idMr.value = id;
    update();
  }

  registerMR() async {
    try {
      var fullName = enterfullnameController.text;

// Splitting the full name into first name and last name
      List nameParts = fullName.split(' ');

// Checking if the split was successful
      var firstName = nameParts.length > 0 ? nameParts[0] : '';
      var lastName = nameParts.length > 1 ? nameParts[1] : '';

// Handling cases where the name may have more than one space
      if (nameParts.length > 2) {
        lastName = nameParts.sublist(1).join(' ');
      }
      Map data = {
        "firstname": firstName,
        "lastname": lastName,
        "phone_firebase":
            Get.find<ProfileController>().profile.value.phoneFirebase,
        "idcard": idcardvalueoneController.text,
        "career": '',
        "province": Get.find<ProfileController>().profile.value.province,
        "district": Get.find<ProfileController>().profile.value.amphur,
        "subdistrict": "",
        "zipcode": "",
      };
      debugPrint("dataMR => $data");
      AppLoading.loadingVerify(Get.context!);
      final response =
          await HttpService.callAPIjwt('POST', Endpoints.registerMR, data);
      AppLoading.Loaderhide(Get.context!);
        debugPrint("response registerMR => $response");
      if (response['status'] == 200) {
        Get.to(() => MrRegisterSuccess());
        // Get.snackbar("สมัครสมาชิก MR สำเร็จ", "สมัครสมาชิก MR สำเร็จ", snackPosition: SnackPosition.BOTTOM);
        // getMRData();
      } else {
        // Get.snackbar("สมัครสมาชิก MR ไม่สำเร็จ", "สมัครสมาชิก MR ไม่สำเร็จ", snackPosition: SnackPosition.BOTTOM);
      }
    } catch (e) {
      if (kDebugMode) {
        print("error registerMR =>$e");
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
      return false;
    }
  }

  referFriend(context) async {
    try {
      // //TODO กำหนดค่าเริ่มต้น activity id Likepoint2.0 สำหรับ POI ของแอป
      // if (AppConfig.of(context).countryConfigCollection.toString() == 'aam') {
      //   Get.put(WebViewPointController()).setInitPOIData();
      // }
      // print("###### referFriend");

      if (isSubmitting.value) return;

      isSubmitting.value = true; //TODO เช็คดักเบิ้ล
      update();

      debugPrint("### เข้าฟังก์ชัน referFriend");
      final ProfileController profileController = Get.find<ProfileController>();
      final WebViewPointController webViewPointCtl =
          Get.find<WebViewPointController>();
      await webViewPointCtl.getActivityPOI(); //TODO ดึงข้อมูล POI ของแอป

      Map data = {
        "firstname": firstNameController.value.text,
        "lastname": lastNameController.value.text,
        "phone_customer": phoneController.value.text,
        "province": selectedProvince!.value,
        "district": selectedDistrict!.value,
        "guarantee": Get.find<LoanController>().selectedGuarantee.value,
        "imgGuarantee": imageList.toString(),
        "mr_id": mrData.value.mr_id ?? profileController.profile.value.ref_code,
        "mr_name": mrData.value.mr_fname! + " " + mrData.value.mr_lname!,
        "mr_phone": Get.find<ProfileController>().profile.value.phoneFirebase,
        "mr_rank": appConfig.countryConfigCollection.toString() == 'aam'
            ? mrData.value.mr_rank
            : "-",
      };
      // print("dataRefer => $data");
      // print("saveReferMRto4no => ${Endpoints.saveReferMRto4no}");
      AppLoading.loadingVerify(Get.context!);
      final response = await HttpService.callAPIjwt(
          'POST', Endpoints.saveReferMRto4no, data);
      // print("responsedataRefer => $response");
      // final response = await HttpService.callAPIjwt('POST', Endpoints.referFriend, data);

      if (response['status'] == 200 &&
          response['result']['statusCode'] == 200) {
        Map data_refer = {
          "running_4no": response['result']['running_4no'],
          "firstname": firstNameController.value.text,
          "lastname": lastNameController.value.text,
          "phone_customer": phoneController.value.text,
          "district": selectedDistrict!.value,
          "province": selectedProvince!.value,
          "branch": response['result']['branch'],
          "guarantee": Get.find<LoanController>().selectedGuarantee!.value,
          "img1": imageList.length > 0 ? imageList[0] : "",
          "img2": imageList.length > 1 ? imageList[1] : "",
          "img3": imageList.length > 2 ? imageList[2] : "",
          "mr_id":
              mrData.value.mr_id ?? profileController.profile.value.ref_code,
          "mr_name": mrData.value.mr_fname! + " " + mrData.value.mr_lname!,
          "mr_phone": Get.find<ProfileController>().profile.value.phoneFirebase,
          "mr_rank": appConfig.countryConfigCollection.toString() == 'aam'
              ? mrData.value.mr_rank
              : "-",
          "merchantID": (webViewPointCtl.merchantID?.value?.isEmpty ?? true)
              ? '-'
              : webViewPointCtl.merchantID!.value!, //TODO set merchantID POI
          "activityID": (webViewPointCtl.referfriend_poi?.value?.isEmpty ?? true)
              ? '-'
              : webViewPointCtl
              .referfriend_poi!.value! //TODO  activity id Likepoint2.0 สำหรับ POI ของแอป
        };
        // print("dataRefer => $data_refer");
        // print("referFriendV2 => ${Endpoints.referFriendV_2}");
        final res_refer = await HttpService.callAPIjwt(
            'POST', Endpoints.referFriendV_2, data_refer);
        print("res_refer => $res_refer");
        if (res_refer['status'] == 200) {
          AppLoading.Loaderhide(Get.context!);

          Get.to(() => ReferFriendSuccess());
          // Get.snackbar("แนะนำเพื่อนสำเร็จ", "แนะนำเพื่อนสำเร็จ", snackPosition: SnackPosition.BOTTOM);
          getReferFriend();
        } else {
          AppLoading.Loaderhide(Get.context!);
          print("error referFriend => ${response['message']}");
        }
      } else {
        AppLoading.Loaderhide(Get.context!);
        // Get.snackbar("แนะนำเพื่อนไม่สำเร็จ", "แนะนำเพื่อนไม่สำเร็จ", snackPosition: SnackPosition.BOTTOM);
        //todo แจ้งเตือนเมื่อไม่สำเร็จ ex. limit การแนะนำเพื่อน date or time  จาก return response ของ API
      }
    } catch (exception, stackTrace) {
      AppLoading.Loaderhide(Get.context!);
      await Sentry.captureException(
        exception,
        stackTrace: stackTrace,
      );
      if (kDebugMode) {
        print("error refer=>$exception");
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
      return false;
    }
  }

  getReferFriend() async {
    print('todo');
    try {
      if (mrData.value.mr_id == null || mrData.value.mr_id.toString().isEmpty) {
        return false;
      }

      Map data = {
        "mr_id": mrData.value.mr_id ??
            Get.find<ProfileController>().profile.value.ref_code,
        "phone_firebase":
            Get.find<ProfileController>().profile.value.phoneFirebase,
      };
      // print(data);
      // print("getReferFriendData");
      final response = await HttpService.callAPIjwt(
          'POST', Endpoints.getReferFriendData, data);
      // print(Endpoints.getReferFriendData);
      // print('response getReferFriendData => $response');
      if (response['status'] == 200) {
        print("loop");
        mrReferral.clear(); // Clear the list before adding new data
        filtered_mrReferral.clear();
        totalPointReferal?.value = 0;
        update();
        close?.value = response['result']['transection_close'];
        pending?.value = response['result']['transection_pending'];
        // print("close => ${close?.value}");
        // print("pending => ${pending?.value}");
        // final  close = response['result']['transection_close'].toString();
        // final  pending = response['result']['transection_pending'].toString();
        for (var i = 0; i < response['result']['transection'].length; i++) {
          // print("loop2");
          // print("########");
          // print(response['result']['transection'][i]);
          final transactionData = response['result']['transection'][i];
          mrReferral.add(ReferralMR(
            mr_name: transactionData['mr_name'],
            cust_name: transactionData['cust_name'],
            cust_phone: transactionData['cust_phone'],
            refcode: transactionData['refcode'],
            status_grant: transactionData['status_grant'],
            status: transactionData['status'],
          ));
          // print("mrReferral => ${mrReferral[i].transection_close}");

          totalPointReferal?.value = (totalPointReferal?.value ?? 0) +
              (int.tryParse(transactionData['likepoint']?.toString() ?? '0') ??
                  0); //TODO คะแนนที่ได้จากการแนะนำเพื่อน, ดาวน์โหลด Likepoint2.0 สำหรับ POI ของแอป
          totalPointReferal?.value = 0; //todo for test UI loose
          update();
        }
        searchMrReferralLoan('');
        //################
        Map<String, List<Map<String, dynamic>>> groupedData = {};

        for (var item in response['result']['transection']) {
          String dateKey =
              item["date"].split("T")[0]; // ใช้เฉพาะวันที่ (YYYY-MM-DD)

          if (!groupedData.containsKey(dateKey)) {
            groupedData[dateKey] = [];
          }
          groupedData[dateKey]!.add(item);
        }

        print("groupedData");
        print(groupedData);
        //################

        filteredUsers = mrReferral;
        update();
        print(mrReferral); // Update the UI
        return mrReferral;
      } else {
        print("error getReferFriend => ${response['message']}");
        // Handle error, e.g., show a snackbar or return an error message
        return false;
      }
    } catch (exception, stackTrace) {
      await Sentry.captureException(
        exception,
        stackTrace: stackTrace,
      );
      if (kDebugMode) {
        print("error refer=>$exception");
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
      return false;
    }
  }

  searchMR(String search) async {
    Map data = {
      "phone_firebase":
          Get.find<ProfileController>().profile.value.phoneFirebase.toString(),
      "mr_code": mrData.value.mr_id.toString(),
      "ref_code":
          Get.find<ProfileController>().profile.value.ref_code.toString(),
      "keyword": search,
      "offset": 0
    };
    final response = await HttpService.callAPIjwt(
        'POST', Endpoints.searchHistoryReferal, data);
    print("searchMR => $response");
    if (response['status'] == 200) {
      for (var i = 0; i < response['result'].length;) {
        filteredUsers = mrReferral
            .where((element) => element.cust_name!.contains(search))
            .toList();
        update();
        print(filteredUsers); // Update the UI
        return filteredUsers;
      }
    }
  }

  getMRReferralHistory() async {
    try {
      print("getMRReferralHistory");
      Map data = {
        "phone_firebase":
            Get.find<ProfileController>().profile.value.phoneFirebase,
      };
      print(data);
      final response = await HttpService.callAPIjwt(
          'POST', Endpoints.getMRReferralHistory, data);
      print("responsegetMRReferralHistory => $response");
      print(response['result']['transection']);
    } catch (e) {
      if (kDebugMode) {
        print("error getMRReferralHistory =>$e");
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
      return false;
    }
  }

  void setIndexPageMr(int index) {
    indexPageMR = index.obs;
    update();
  }

  void setNextPageMr() {
    if (indexPageMR!.value != 2) {
      indexPageMR!.value = indexPageMR!.value + 1;
      update();
    }
  }

  void setFinalPage() {
    isFinalPageMR!.value = true;
    update();
  }

  void pickImage() async {
    final ImagePicker _picker = ImagePicker();
    final pickedImage = await _picker.pickImage(source: ImageSource.gallery);
    imageFile = pickedImage != null ? File(pickedImage.path) : null;
    print("imageFile:${imageFile}");
    upLoadToS3();
  }

  Future<dynamic> openCamera(context) async {
    final ImagePicker _picker = ImagePicker();

    final cameraImage = await _picker.pickImage(source: ImageSource.camera);

    File imageFile = File(cameraImage!.path);
    List<int> imageBytes = imageFile.readAsBytesSync();
    String base64Image = base64Encode(imageBytes);
    upLoadToS3();
  }

  void upLoadToS3() async {
    print("upLoadToS3");
    String base64Image = base64Encode(imageFile!.readAsBytesSync());
    print("base64Image:${base64Image}");
    print("ตรงนี้${appConfig.countryConfigCollection}");
    var name = "";
    var folder = "";
    if (appConfig.countryConfigCollection.toString() == 'aam') {
      name = "MappAAM";
      folder = "MappAAM/requestLoan";
    } else if (appConfig.countryConfigCollection.toString() == 'rafco') {
      name = "MappRafco";
      folder = "MappRafco/ImageVehicle";
    } else {
      name = "MappRPLC";
      folder = "MappRPLC/VmotorImage";
    }
    print(base64Image);
    print("ปริ้นตรงนี้");
    if (base64Image != "" || base64Image != null) {
      Map map = {"name": name, "folder": folder, "image": base64Image};
      print("MAP:${map}");
      final response = await HttpService.post(
          Endpoints.uploadS3_Center, map, HttpService.noKeyRequestHeaders);
      print(response);

      // var jsonResponse = json.decode(response);

      if (response["statusCode"].toString() == "200") {
        imageList.add(response["result"]["url"]["Location"]);
        update();
        print("imageList:${imageList.length.toString()}");
        // Navigator.pop();
      } else {
        // Fluttertoast.showToast(
        //     msg: "upload fail!",
        //     toastLength: Toast.LENGTH_SHORT,
        //     gravity: ToastGravity.TOP,
        //     timeInSecForIosWeb: 1,
        //     backgroundColor: Colors.redAccent.withOpacity(0.5),
        //     textColor: Colors.black,
        //     fontSize: 16.0);
      }
    } else {
      //   Fluttertoast.showToast(
      //       msg: "cancle upload",
      //       toastLength: Toast.LENGTH_SHORT,
      //       gravity: ToastGravity.TOP,
      //       timeInSecForIosWeb: 1,
      //       backgroundColor: Colors.redAccent.withOpacity(0.5),
      //       textColor: Colors.black,
      //       fontSize: 16.0);
      // }
    }
  }

  final RegisterAddressController addressController =
      Get.put(RegisterAddressController());
  void setDropdown(context, String type, int value) {
    if (type == accountAddressProvince.tr) {
      final regisAddress = Get.find<RegisterAddressController>();
      print('city code : ${regisAddress.citiesData[value].cityId}');
      selectedProvince!.value =
          regisAddress.citiesData[value].cityNameLocal.toString();
      regisAddress.setSelectedCityCode(regisAddress.citiesData[value].cityId!);
      // ProvinceComplete!.value = true;
      update();
    } else {
      final regisAddress = Get.find<RegisterAddressController>();
      selectedDistrict!.value =
          regisAddress.districtsData[value].districtNameLocal.toString();
      // DistrictComplete!.value = true;
      update();
    }
    update();
    Navigator.pop(context);
  }

  void setInitialRefCode() {
    referCode.text =
        Get.find<ProfileController>().profile.value.ref_code_other ?? '';
    update();
  }

  Future<void> setRefCodeData(String value) async {
    referCode.text = value ?? '';
    update();
  }

  Future<dynamic> addReferalCode() async {
    try {
      print("addReferalCode");

      Map data = {
        "phone_firebase":
            Get.find<ProfileController>().profile.value.phoneFirebase,
        "ref_code": referCode.text,
      };

      AppLoading.loadingVerify(Get.context!);

      final response = await HttpService.callAPIjwt(
          'POST', Endpoints.updateReferalCode, data);
      AppLoading.Loaderhide(Get.context!);
      if (response['status'] == 200) {
        // debugPrint("response addReferalCode => $response");
        referCode.text = response['result']['ref_code'];
        update();
        await Get.find<ProfileController>()
            .updateRefcodeData(response['result']['ref_code']);

        showDialog(
            useSafeArea: false,
            context: Get.context!,
            builder: (_) => SuccessAddReferralCode());
      } else {
        print("error addReferalCode => ${response['message']}");
        referCode.text = "";
        update();
        Get.snackbar("error", "กรุณากรอก Referral Code ใหม่");
      }
    } catch (e) {
      if (kDebugMode) {
        print("error addReferalCode =>$e");
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
      return false;
    }
  }

  void addReferralDownload(ReferralDownload customer) {
    referral_download.add(customer);
    update();

    // print("########");
    // print("referral_download => ${referral_download}");
    // print("referral_download => ${referral_download.value[0].firstName}");
  }

  void removeReferralDownload(ReferralDownload customer) {
    referral_download.remove(customer);
    update();
  }

  void setReferralDownload(List<ReferralDownload> customerList) {
    referral_download.assignAll(customerList);
    update();
  }

  Future<void> getMRReferralDownload() async {
    try {
      Map data = {
        "phone_firebase": Get.find<ProfileController>()
            .profile
            .value
            .phoneFirebase
            .toString(),
        "mr_code": mrData.value.mr_id.toString() ?? "",
        "ref_code":
            Get.find<ProfileController>().profile.value.ref_code.toString() ??
                ""
      };
      final response = await HttpService.callAPIjwt(
          'POST', Endpoints.getReferralDownload, data);

      if (response['status'] == 200) {
        referral_download.clear();
        filtered_referral_download.clear();
        for (var i = 0; i < response['result'].length; i++) {
          referral_download.add(ReferralDownload(
              createTime: response['result'][i]['create_time'],
              firstName: response['result'][i]['cust_fname'],
              lastName: response['result'][i]['cust_lname'],
              phone: response['result'][i]['cust_phone'],
              imageUrl: response['result'][i]['cust_img'],
              refCode: response['result'][i]['cust_ref_code']));
          update();
        }
        searchCustomer('');
      }
    } catch (e) {
      if (kDebugMode) {
        print("error getMRReferralDownload =>$e");
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }
  }

  void searchCustomer(String query) {
    // print("searchCustomer");
    if (query.isEmpty) {
      filtered_referral_download.value = List.from(referral_download);
      update();
    } else {
      filtered_referral_download.value = referral_download
          .where((customer) =>
              customer.firstName.toLowerCase().contains(query.toLowerCase()) ||
              customer.lastName.toLowerCase().contains(query.toLowerCase()) ||
              customer.phone.contains(query) ||
              customer.refCode.toLowerCase().contains(query.toLowerCase()))
          .toList();
      update();

    }
    // print("filtered_referral_download => ${filtered_referral_download.length}");

  }


  void searchMrReferralLoan(String query) {
    if (query.isEmpty) {
      filtered_mrReferral.value = List.from(mrReferral);
      update();
    } else {
      filtered_mrReferral.value = mrReferral.where((mr) =>
      mr.mr_name.toString().contains(query.toLowerCase()) ||
          mr.cust_name.toString().contains(query.toLowerCase()) ||
          mr.cust_phone.toString().contains(query) ||
          mr.refcode.toString().contains(query.toLowerCase()) ||
          mr.status_grant.toString().contains(query.toLowerCase()) ||
          mr.status.toString().contains(query.toLowerCase())
      ).toList();
      update();
    }
  }
}
