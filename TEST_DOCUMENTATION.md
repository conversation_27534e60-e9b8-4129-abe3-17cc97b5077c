# BranchScreen Testing Documentation

## ภาพรวม
เอกสารนี้อธิบายการทดสอบ Unit Tests สำหรับ BranchScreen และ BranchController ในโปรเจค AAMG รวมถึงโครงสร้างการทดสอบ วิธีการรัน และการตีความผลลัพธ์

## โครงสร้างการทดสอบ

### 1. Test Categories
```
test/
├── unit_test/                  # Unit Tests
│   └── branch_screen_test.dart
├── widget_test/                # Widget Tests  
│   └── branch_screen_widget_test.dart
├── integration_test/           # Integration Tests
│   └── branch_controller_test.dart
├── mocks/                      # Mock Classes
│   ├── mock_branch_controller.dart
│   └── mock_http_service.dart
└── helpers/                    # Test Utilities
    └── test_helpers.dart
```

### 2. Test Reports Structure
```
test_reports/
├── README.md                   # การใช้งาน test reports
├── unit_tests/                 # รายงาน Unit Tests
├── widget_tests/               # รายงาน Widget Tests
├── integration_tests/          # รายงาน Integration Tests
├── coverage/                   # รายงาน Test Coverage
└── test_summary.md            # สรุปผลการทดสอบ
```

## การทดสอบแต่ละประเภท

### Unit Tests (test/unit_test/branch_screen_test.dart)
ทดสอบฟังก์ชันการทำงานพื้นฐานของ BranchScreen

**Test Groups:**
- Widget Creation Tests: ทดสอบการสร้าง widget
- Search Functionality Tests: ทดสอบการค้นหา
- Filter Functionality Tests: ทดสอบการกรอง (AAM only)
- Data Display Tests: ทดสอบการแสดงข้อมูล
- Refresh Functionality Tests: ทดสอบการ refresh
- Navigation Tests: ทดสอบการนำทาง
- Error Handling Tests: ทดสอบการจัดการ error

### Widget Tests (test/widget_test/branch_screen_widget_test.dart)
ทดสอบ UI components และ user interactions

**Test Groups:**
- UI Components Tests: ทดสอบการแสดงผล UI
- Search Field Interaction Tests: ทดสอบการใช้งาน search field
- Branch List Display Tests: ทดสอบการแสดงรายการสาขา
- Refresh Functionality Tests: ทดสอบ pull-to-refresh
- Responsive Design Tests: ทดสอบ responsive behavior
- Accessibility Tests: ทดสอบ accessibility

### Integration Tests (test/integration_test/branch_controller_test.dart)
ทดสอบการทำงานของ BranchController methods

**Test Groups:**
- getBranchData Tests: ทดสอบการโหลดข้อมูล
- searchBranchData Tests: ทดสอบการค้นหา
- sortBranchDataByZone Tests: ทดสอบการกรองตาม zone
- setIntialBranchData Tests: ทดสอบการรีเซ็ตข้อมูล
- State Management Tests: ทดสอบการจัดการ state

## Mock Classes และ Test Utilities

### MockBranchController
```dart
class MockBranchController extends GetxController {
  // Mock flags สำหรับการทดสอบ
  bool shouldFailGetBranchData = false;
  bool shouldFailSearchBranchData = false;
  bool isLoading = false;
  
  // Helper methods
  void setMockData(List<Branch> data);
  void simulateApiError();
  void resetApiError();
}
```

### MockHttpService
```dart
class MockHttpService {
  static Future<Map<String, dynamic>> callAPIjwt(
    String method, String endpoint, Map<String, dynamic> data
  );
  
  // Helper methods
  static void setCustomResponse(Map<String, dynamic> response);
  static void simulateError();
  static List<Map<String, dynamic>> getCallHistory();
}
```

### TestHelpers
```dart
class TestHelpers {
  // Widget testing utilities
  static Widget createTestableWidget(Widget child);
  static Future<void> waitForAnimations(WidgetTester tester);
  static Future<void> tapAndWait(WidgetTester tester, Finder finder);
  
  // Mock data creation
  static List<Branch> createMockBranchData();
  static Map<String, dynamic> createMockGetBranchResponse();
  
  // GetX utilities
  static void setupGetXForTesting();
  static void cleanupGetX();
}
```

## วิธีการรันการทดสอบ

### 1. รันการทดสอบทั้งหมด
```bash
# ใช้ test runner script
./test_runner.sh

# หรือรันด้วย Flutter command
flutter test
```

### 2. รันการทดสอบแต่ละประเภท
```bash
# Unit Tests
flutter test test/unit_test/

# Widget Tests  
flutter test test/widget_test/

# Integration Tests
flutter test test/integration_test/
```

### 3. รันพร้อม Coverage
```bash
# รัน tests พร้อม coverage report
flutter test --coverage

# สร้าง HTML coverage report
genhtml coverage/lcov.info -o test_reports/coverage/html/
```

## การตีความผลลัพธ์

### Test Results
```
✓ PASS: Test ผ่าน
✗ FAIL: Test ไม่ผ่าน  
⚠ SKIP: Test ถูกข้าม
```

### Coverage Metrics
- **Line Coverage**: เปอร์เซ็นต์บรรทัดโค้ดที่ถูกทดสอบ
- **Function Coverage**: เปอร์เซ็นต์ฟังก์ชันที่ถูกทดสอบ
- **Branch Coverage**: เปอร์เซ็นต์ conditional logic ที่ถูกทดสอบ

### เป้าหมาย Coverage
- Unit Tests: >= 80%
- Widget Tests: >= 70%
- Integration Tests: >= 60%
- Overall Coverage: >= 75%

## Best Practices

### 1. การเขียน Tests
- ใช้ AAA pattern (Arrange, Act, Assert)
- เขียน test descriptions ที่ชัดเจน
- ทดสอบทั้ง happy path และ edge cases
- ใช้ mock data ที่สมจริง

### 2. การจัดการ Mock
- ใช้ mock classes สำหรับ external dependencies
- Reset mock state ใน setUp/tearDown
- ตรวจสอบ mock interactions

### 3. การจัดการ State
- ใช้ GetX test mode
- Cleanup controllers หลังแต่ละ test
- ตรวจสอบ state changes

## Troubleshooting

### ปัญหาที่พบบ่อย

1. **GetX Controller Issues**
   ```dart
   // Solution: ใช้ Get.testMode และ cleanup
   setUp(() {
     Get.testMode = true;
   });
   
   tearDown(() {
     Get.reset();
   });
   ```

2. **Widget Testing Issues**
   ```dart
   // Solution: ใช้ TestHelpers.createTestableWidget()
   await tester.pumpWidget(
     TestHelpers.createTestableWidget(BranchScreen()),
   );
   ```

3. **Async Testing Issues**
   ```dart
   // Solution: รอให้ animations เสร็จสิ้น
   await TestHelpers.waitForAnimations(tester);
   ```

## การบำรุงรักษา Tests

### 1. การอัพเดท Tests
- อัพเดท tests เมื่อมีการเปลี่ยนแปลง business logic
- เพิ่ม test cases สำหรับ features ใหม่
- รักษา mock data ให้เป็นปัจจุบัน

### 2. การตรวจสอบ Quality
- รัน tests อย่างสม่ำเสมอ
- ตรวจสอบ coverage reports
- Review และ refactor tests เมื่อจำเป็น

### 3. การจัดเก็บ Reports
- เก็บ test reports ใน test_reports/ folder
- อัพเดท documentation เมื่อมีการเปลี่ยนแปลง
- ใช้ version control สำหรับ test history

## Resources

### เอกสารเพิ่มเติม
- [Flutter Testing Guide](https://flutter.dev/docs/testing)
- [GetX Testing](https://github.com/jonataslaw/getx#testing)
- [Widget Testing](https://flutter.dev/docs/cookbook/testing/widget)

### Tools และ Libraries
- flutter_test: Flutter testing framework
- get: State management และ testing utilities
- flutter_screenutil: Responsive design testing
- mockito: Advanced mocking (optional)

---

**หมายเหตุ**: เอกสารนี้จะถูกอัพเดทเมื่อมีการเปลี่ยนแปลงในการทดสอบหรือเพิ่ม features ใหม่
