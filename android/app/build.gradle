plugins {
    id "com.android.application"
    id 'com.google.gms.google-services'
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode') ?: '4'
def flutterVersionName = localProperties.getProperty('flutter.versionName') ?: '4.0.0'

def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file('key.properties')
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}

android {
    namespace "com.aamfinancegroup.aambeta"
    compileSdkVersion 35
    ndkVersion "25.1.8937393"

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    aaptOptions {
        noCompress "tflite", "lite"
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    lintOptions {
        disable 'InvalidPackage'
        checkReleaseBuilds false
    }

    defaultConfig {
        applicationId "com.aamfinancegroup.aambeta"
        minSdkVersion 24
        targetSdkVersion flutter.targetSdkVersion
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
        multiDexEnabled true
    }

    signingConfigs {
        release {
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null
            storePassword keystoreProperties['storePassword']
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            shrinkResources false
            signingConfig signingConfigs.release
        }
    }

    flavorDimensions "env"
    productFlavors {
        aam_dev {
            dimension "env"
            resValue "string", "app_name", "AAM(BETA)"
            resValue "string", "keytool_sha1","C9:E6:E7:3D:4F:12:D2:7D:57:A9:A8:09:C2:21:75:92:AB:59:AD:95"
            resValue "string", "google_maps_api_key", "\"AIzaSyCBCxWAr6qPfU78eCzhBIetUToNoxlNOhM\""
//            resValue "string", "facebook_app_id","1913575202110061"
//            resValue "string", "fb_login_protocol_scheme","fb1913575202110061"
            applicationId "com.aamfinancegroup.aambeta"
        }

        aam_prod {
            dimension "env"
            resValue "string", "app_name", "AAM"
            resValue "string", "keytool_sha1","C9:E6:E7:3D:4F:12:D2:7D:57:A9:A8:09:C2:21:75:92:AB:59:AD:95"
            resValue "string", "google_maps_api_key", "\"AIzaSyCBCxWAr6qPfU78eCzhBIetUToNoxlNOhM\""
//            resValue "string", "facebook_app_id","\"1913575202110061\""
//            resValue "string", "fb_login_protocol_scheme","\"fb1913575202110061\""
            applicationId "com.aamfinancegroup.aam"
        }

        rafco_dev {
            dimension "env"
            resValue "string", "app_name", "RAFCO(BETA)"
            resValue "string", "keytool_sha1","39:E4:16:8B:7C:CF:C1:2D:5B:4A:A1:B1:DB:E4:8F:F4:82:2A:BF:B1"
            resValue "string", "google_maps_api_key", "\"AIzaSyC-RD0kTkFNbpL7d3yjChhoPYHsbRonuHA\""
//            resValue "string", "facebook_app_id","\"1913575202110061\""
//            resValue "string", "fb_login_protocol_scheme","\"fb713437833144457\""
            applicationId "com.rptn.rafcobeta"
        }

        rafco_prod {
            dimension "env"
            resValue "string", "app_name", "RAFCO"
            resValue "string", "keytool_sha1","9E:F7:CD:D2:2E:88:09:DC:C8:D4:CD:5B:AD:ED:B6:63:91:66:79:59"
            resValue "string", "google_maps_api_key", "\"AIzaSyC-RD0kTkFNbpL7d3yjChhoPYHsbRonuHA\""
//            resValue "string", "facebook_app_id","\"1913575202110061\""
//            resValue "string", "fb_login_protocol_scheme","\"fb713437833144457\""
            applicationId "com.rptn.rafco"
        }

        rplc_dev {
            dimension "env"
            resValue "string", "app_name", "RPLC(BETA)"
            resValue "string", "keytool_sha1","BE:B5:84:52:9B:56:AF:6E:4C:A5:24:54:E1:3E:99:49:57:67:27:F4"
            resValue "string", "google_maps_api_key", "\"AIzaSyDV7DZQAHVMO1KtuEOpM95DRsn8SBeXq1w\""
//            resValue "string", "facebook_app_id","\"713437833144457\""
//            resValue "string", "fb_login_protocol_scheme","\"fb713437833144457\""
            applicationId "com.ruampattanaleasing.rplc_appbeta"
        }

        rplc_prod {
            dimension "env"
            resValue "string", "app_name", "RPLC"
            resValue "string", "keytool_sha1","9E:F7:CD:D2:2E:88:09:DC:C8:D4:CD:5B:AD:ED:B6:63:91:66:79:59"
            resValue "string", "google_maps_api_key", "\"AIzaSyDV7DZQAHVMO1KtuEOpM95DRsn8SBeXq1w\""
//            resValue "string", "facebook_app_id","\"713437833144457\""
//            resValue "string", "fb_login_protocol_scheme","\"fb713437833144457\""
            applicationId "com.ruampattanaleasing.rplc_app"
        }
    }
}

flutter {
    source '../..'
}

dependencies {
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk8:$kotlin_version"
    implementation "androidx.multidex:multidex:2.0.1"
    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:2.0.4'
    implementation platform('com.google.firebase:firebase-bom:33.1.0')
    implementation 'com.google.android.gms:play-services-maps:19.0.0'
    implementation 'com.google.android.gms:play-services-auth:21.2.0'
}