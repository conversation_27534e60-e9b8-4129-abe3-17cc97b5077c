# LoanMainScreen Testing Documentation

## ภาพรวม
เอกสารนี้อธิบายการทดสอบ Unit Tests สำหรับ LoanMainScreen ในโปรเจค AAMG รวมถึงโครงสร้างการทดสอบ วิธีการรัน และการตีความผลลัพธ์

## โครงสร้างการทดสอบ LoanMainScreen

### 1. Test Categories
```
test/
├── unit_test/
│   ├── branch_screen_test.dart         # BranchScreen unit tests
│   └── loan_main_screen_test.dart      # LoanMainScreen unit tests
├── widget_test/
│   ├── branch_screen_widget_test.dart  # BranchScreen widget tests
│   └── loan_main_screen_widget_test.dart # LoanMainScreen widget tests
├── integration_test/
│   ├── branch_controller_test.dart     # BranchController integration
│   └── loan_main_screen_integration_test.dart # LoanMainScreen integration
├── mocks/
│   ├── mock_branch_controller.dart     # BranchScreen mocks
│   ├── mock_contract_list_controller.dart # LoanMainScreen mocks
│   ├── mock_home_controller.dart
│   ├── mock_profile_controller.dart
│   ├── mock_loan_controller.dart
│   └── mock_http_service.dart
└── helpers/
    └── test_helpers.dart               # Shared test utilities
```

### 2. Test Reports Structure
```
test_reports/
├── unit_tests/
│   ├── branch_screen_tests.md
│   ├── branch_controller_tests.md
│   └── loan_main_screen_tests.md       # LoanMainScreen unit test report
├── widget_tests/
│   ├── ui_component_tests.md
│   └── loan_main_screen_widget_tests.md # LoanMainScreen widget test report
├── integration_tests/
│   └── loan_main_screen_integration_tests.md # LoanMainScreen integration report
└── coverage/
    └── html/                           # Coverage reports
```

## LoanMainScreen การทดสอบแต่ละประเภท

### Unit Tests (test/unit_test/loan_main_screen_test.dart)
ทดสอบฟังก์ชันการทำงานพื้นฐานของ LoanMainScreen

**Test Groups:**
- Widget Creation Tests: ทดสอบการสร้าง widget
- Loan Default State Tests: ทดสอบการแสดงผลเมื่อไม่มี loan data
- Loan Request State Tests: ทดสอบการแสดงผลเมื่อมี loan data
- Primary Button Tests: ทดสอบ button functionality
- Navigation Tests: ทดสอบการนำทาง
- State Management Tests: ทดสอบการจัดการ state
- Error Handling Tests: ทดสอบการจัดการ error

### Widget Tests (test/widget_test/loan_main_screen_widget_test.dart)
ทดสอบ UI components และ user interactions

**Test Groups:**
- UI Components Tests: ทดสอบการแสดงผล UI
- Loan Default View Tests: ทดสอบ default view layout
- Loan Request View Tests: ทดสอบ request view layout
- Primary Button Interaction Tests: ทดสอบ button interactions
- Navigation Tests: ทดสอบ navigation behavior
- Responsive Design Tests: ทดสอบ responsive behavior
- State Update Tests: ทดสอบ UI state updates

### Integration Tests (test/integration_test/loan_main_screen_integration_test.dart)
ทดสอบการทำงานของ controllers และ business logic

**Test Groups:**
- Contract Data Management Tests: ทดสอบการจัดการข้อมูล contract
- Contract Status Management Tests: ทดสอบการจัดการ contract status
- User State Management Tests: ทดสอบการจัดการ user state
- Profile Management Tests: ทดสอบการจัดการ profile
- Loan Controller Integration Tests: ทดสอบ loan controller
- Business Logic Integration Tests: ทดสอบ business logic
- Error Handling Integration Tests: ทดสอบ error handling
- Data Consistency Tests: ทดสอบ data consistency

## Mock Classes สำหรับ LoanMainScreen

### MockContractListController
```dart
class MockContractListController extends GetxController {
  // Contract data management
  RxList<ContractList> contractList = <ContractList>[].obs;
  RxList<ContractPending> contractStatus = <ContractPending>[].obs;
  
  // Helper methods
  void setMockContractData(List<ContractList> contracts);
  void setMockContractStatusData(List<ContractPending> statuses);
  String formatCurrency(String input);
  String getGuaranteeTypeName(int index);
}
```

### MockHomeController
```dart
class MockHomeController extends GetxController {
  RxBool? isGuest = false.obs;
  RxBool? showLoanData = false.obs;
  
  // Helper methods
  void setMockGuestStatus(bool isGuest);
  void setMockShowLoanData(bool showData);
}
```

### MockProfileController
```dart
class MockProfileController extends GetxController {
  Rx<Profile> profile = Profile().obs;
  
  // Helper methods
  void setMockCompleteAddress(bool hasAddress);
  bool get hasCompleteAddress;
}
```

### MockLoanController
```dart
class MockLoanController extends GetxController {
  RxBool? isAcceptedTermPolicy = false.obs;
  
  // Helper methods
  Future<void> resetAcceptTermPolicy();
  bool get wasResetCalled;
}
```

## LoanMainScreen Business Logic Testing

### User Flow Testing
```dart
// Guest User Flow
homeController.setMockGuestStatus(true);
// Expected: Show register dialog

// Incomplete Profile Flow
homeController.setMockGuestStatus(false);
profileController.setMockCompleteAddress(false);
// Expected: Show address update alert

// Complete Profile Flow
homeController.setMockGuestStatus(false);
profileController.setMockCompleteAddress(true);
// Expected: Proceed to loan agreement
```

### Contract Data Testing
```dart
// Empty Contract List
contractListController.setMockContractData([]);
// Expected: Show loan default view

// With Contract Data
final mockContracts = TestHelpers.createMockContractData();
contractListController.setMockContractData(mockContracts.cast());
// Expected: Show loan request view
```

### State Management Testing
```dart
// State Transitions
expect(contractListController.hasContracts, isFalse); // Default state
contractListController.setMockContractData(mockData);
expect(contractListController.hasContracts, isTrue); // Request state
```

## วิธีการรันการทดสอบ LoanMainScreen

### 1. รันการทดสอบทั้งหมด
```bash
# ใช้ test runner script
./test_runner.sh

# หรือรันเฉพาะ LoanMainScreen tests
flutter test test/unit_test/loan_main_screen_test.dart
flutter test test/widget_test/loan_main_screen_widget_test.dart
flutter test test/integration_test/loan_main_screen_integration_test.dart
```

### 2. รันพร้อม Coverage
```bash
flutter test --coverage test/unit_test/loan_main_screen_test.dart
flutter test --coverage test/widget_test/loan_main_screen_widget_test.dart
flutter test --coverage test/integration_test/loan_main_screen_integration_test.dart
```

## Test Results Summary

### LoanMainScreen Test Statistics
```
Unit Tests: 15+ test cases
- Widget Creation: 3 tests
- Loan Default State: 2 tests
- Loan Request State: 3 tests
- Primary Button: 3 tests
- Navigation: 1 test
- State Management: 2 tests
- Error Handling: 1 test

Widget Tests: 25+ test cases
- UI Components: 3 tests
- Loan Default View: 3 tests
- Loan Request View: 3 tests
- Button Interactions: 3 tests
- Navigation: 2 tests
- Responsive Design: 2 tests
- State Updates: 1 test

Integration Tests: 30+ test cases
- Contract Data Management: 5 tests
- Contract Status Management: 3 tests
- User State Management: 3 tests
- Profile Management: 3 tests
- Loan Controller Integration: 4 tests
- Business Logic Integration: 4 tests
- Error Handling: 2 tests
- Data Consistency: 2 tests

Total LoanMainScreen Tests: 70+ test cases
```

### Coverage Goals สำหรับ LoanMainScreen
- Unit Tests: >= 85%
- Widget Tests: >= 75%
- Integration Tests: >= 80%
- Overall Coverage: >= 80%

## Best Practices สำหรับ LoanMainScreen Testing

### 1. Mock Data Management
```dart
// ใช้ TestHelpers สำหรับ mock data
final mockContracts = TestHelpers.createMockContractData();
final mockStatuses = TestHelpers.createMockContractStatusData();

// Setup mock controllers
contractListController.setMockContractData(mockContracts.cast());
contractListController.setMockContractStatusData(mockStatuses.cast());
```

### 2. State Testing
```dart
// Test state transitions
expect(controller.initialState, expectedInitialValue);
controller.performAction();
expect(controller.finalState, expectedFinalValue);
```

### 3. User Flow Testing
```dart
// Test complete user flows
testWidgets('should handle complete loan application flow', (tester) async {
  // Setup user state
  homeController.setMockGuestStatus(false);
  profileController.setMockCompleteAddress(true);
  
  // Perform actions
  await tester.tap(find.byType(PrimaryButton));
  
  // Verify results
  expect(/* loan agreement flow triggered */, isTrue);
});
```

## Troubleshooting LoanMainScreen Tests

### ปัญหาที่พบบ่อย

1. **Controller Dependencies**
   ```dart
   // Solution: Setup all required controllers
   Get.put<ContractListController>(mockContractListController);
   Get.put<HomeController>(mockHomeController);
   Get.put<ProfileController>(mockProfileController);
   Get.put<LoanController>(mockLoanController);
   ```

2. **Mock Data Issues**
   ```dart
   // Solution: ใช้ TestHelpers สำหรับ consistent mock data
   final mockData = TestHelpers.createMockContractData();
   controller.setMockContractData(mockData.cast());
   ```

3. **State Management Issues**
   ```dart
   // Solution: รอให้ state updates เสร็จสิ้น
   await TestHelpers.waitForAnimations(tester);
   await tester.pump(); // Force rebuild
   ```

## การบำรุงรักษา LoanMainScreen Tests

### 1. การอัพเดท Tests
- อัพเดท tests เมื่อมีการเปลี่ยนแปลง LoanMainScreen UI
- เพิ่ม test cases สำหรับ features ใหม่
- รักษา mock data ให้เป็นปัจจุบัน

### 2. การตรวจสอบ Quality
- รัน tests อย่างสม่ำเสมอ
- ตรวจสอบ coverage reports
- Review และ refactor tests เมื่อจำเป็น

### 3. การจัดเก็บ Reports
- อัพเดท test reports ใน test_reports/ folder
- เก็บ coverage history
- Document test changes

---

**หมายเหตุ**: เอกสารนี้จะถูกอัพเดทเมื่อมีการเปลี่ยนแปลงใน LoanMainScreen หรือเพิ่ม features ใหม่
